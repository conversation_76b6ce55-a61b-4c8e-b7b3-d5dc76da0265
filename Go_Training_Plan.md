# Go Programming Training Plan for Automation and Integration Team

## Overview

This comprehensive 32-session training plan is designed for beginners in Go programming who will be working on automation and integration projects. Each session is structured for 1 hour with a balance of theory (45 minutes) and hands-on practice (15 minutes).

## Prerequisites

- Basic programming experience in any language
- Understanding of fundamental programming concepts (variables, functions, loops, etc.)
- Familiarity with command-line interfaces
- Development environment setup (Go 1.24+, VS Code or similar IDE)

## Course Structure

- **Total Sessions**: 32 sessions
- **Duration**: 1 hour per session
- **Format**: 45 minutes theory + 15 minutes hands-on practice
- **Target Audience**: Automation and Integration team members

---

## Table of Contents

### Phase 1: Foundation (Sessions 1-8)

1. [Go Environment Setup and Language Introduction](#session-1-go-environment-setup-and-language-introduction)
2. [Variables and Type System](#session-2-variables-and-type-system)
3. [Struct Types and Memory Layout](#session-3-struct-types-and-memory-layout)
4. [Pointers Part 1: Pass by Value and Sharing Data](#session-4-pointers-part-1-pass-by-value-and-sharing-data)
5. [Pointers Part 2: Escape Analysis and Memory Management](#session-5-pointers-part-2-escape-analysis-and-memory-management)
6. [Pointers Part 3: Stack Growth and Garbage Collection](#session-6-pointers-part-3-stack-growth-and-garbage-collection)
7. [Constants and Type Safety](#session-7-constants-and-type-safety)
8. [Data-Oriented Design Principles](#session-8-data-oriented-design-principles)

### Phase 2: Data Structures (Sessions 9-14)

1. [Arrays: Mechanical Sympathy and Performance](#session-9-arrays-mechanical-sympathy-and-performance) *(Session 9)*
2. [Arrays: Semantics and Value Types](#session-10-arrays-semantics-and-value-types) *(Session 10)*
3. [Slices Part 1: Declaration, Length, and Reference Types](#session-11-slices-part-1-declaration-length-and-reference-types) *(Session 11)*
4. [Slices Part 2: Appending and Memory Management](#session-12-slices-part-2-appending-and-memory-management) *(Session 12)*
5. [Slices Part 3: Slicing Operations and References](#session-13-slices-part-3-slicing-operations-and-references) *(Session 13)*
6. [Strings, Maps, and Range Mechanics](#session-14-strings-maps-and-range-mechanics) *(Session 14)*

<!-- The following sessions are planned but not yet implemented -->

### Phase 3: Object-Oriented Concepts (Sessions 15-20)

1. [Methods Part 1: Declaration and Receiver Behavior](#session-15-methods-part-1-declaration-and-receiver-behavior) *(Session 15)*
2. [Methods Part 2: Value vs Pointer Semantics](#session-16-methods-part-2-value-vs-pointer-semantics) *(Session 16)*
3. [Methods Part 3: Function Variables and Method Sets](#session-17-methods-part-3-function-variables-and-method-sets) *(Session 17)*
4. [Interfaces Part 1: Polymorphism and Design](#session-18-interfaces-part-1-polymorphism-and-design) *(Session 18)*
5. [Interfaces Part 2: Method Sets and Storage](#session-19-interfaces-part-2-method-sets-and-storage) *(Session 19)*
6. [Embedding, Exporting, and Composition Patterns](#session-20-embedding-exporting-and-composition-patterns) *(Session 20)*

### Phase 4: Advanced Composition (Sessions 21-23)

1. [Grouping Types and Decoupling Strategies](#session-21-grouping-types-and-decoupling-strategies) *(Session 21)*
2. [Interface Conversions, Assertions, and Design Guidelines](#session-22-interface-conversions-assertions-and-design-guidelines) *(Session 22)*
3. [Mocking and Testing Strategies](#session-23-mocking-and-testing-strategies) *(Session 23)*

### Phase 5: Error Handling (Sessions 24-25)

1. [Error Handling: Values, Variables, and Context](#session-24-error-handling-values-variables-and-context) *(Session 24)*
2. [Advanced Error Handling: Wrapping and Debugging](#session-25-advanced-error-handling-wrapping-and-debugging) *(Session 25)*

### Phase 6: Code Organization (Sessions 26-27)

1. [Package Design and Language Mechanics](#session-26-package-design-and-language-mechanics) *(Session 26)*
2. [Package-Oriented Design and Best Practices](#session-27-package-oriented-design-and-best-practices) *(Session 27)*

### Phase 7: Concurrency Fundamentals (Sessions 28-30)

1. [Scheduler Mechanics and Goroutines](#session-28-scheduler-mechanics-and-goroutines) *(Session 28)*
2. [Data Races and Synchronization](#session-29-data-races-and-synchronization) *(Session 29)*
3. [Channels and Signaling Semantics](#session-30-channels-and-signaling-semantics) *(Session 30)*

### Phase 8: Advanced Concurrency and Quality (Sessions 31-32)

1. [Advanced Channel Patterns and Context](#session-31-advanced-channel-patterns-and-context) *(Session 31)*
2. [Testing, Benchmarking, and Performance Analysis](#session-32-testing-benchmarking-and-performance-analysis) *(Session 32)*

---

## Session Details

### Session 1: Go Environment Setup and Language Introduction

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Set up Go development environment with latest tooling (Go 1.24+)
- Understand Go's design philosophy and use cases in automation
- Learn basic Go toolchain commands and workspace management
- Create first Go program with modern best practices

**Videos Covered**:

- Course introduction and overview (conceptual)

**Key Concepts**:

- Go installation and GOPATH vs Go modules
- Go workspaces for multi-module projects
- Basic project structure and naming conventions
- Introduction to `go mod`, `go build`, `go run`
- VS Code setup with Go extension

**Hands-on Exercise**:

```go
// Create a simple automation utility
package main

import (
    "fmt"
    "os"
    "time"
)

func main() {
    fmt.Printf("System Monitor started at %v\n", time.Now())
    fmt.Printf("Current working directory: %s\n", getCurrentDir())
}

func getCurrentDir() string {
    dir, err := os.Getwd()
    if err != nil {
        return "unknown"
    }
    return dir
}
```

**Prerequisites**: None

---

### Session 2: Variables and Type System

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master the fundamental principle that "Type is Life" in Go programming
- Understand how type provides size and representation information
- Learn Go's built-in types and their memory characteristics
- Master zero values as an integrity feature
- Understand variable declaration patterns and when to use each
- Grasp the relationship between type, memory, and performance
- Learn Go's conversion vs casting philosophy

**Videos Covered**:

- 2.1 Topics (0:00:48)
- 2.2 Variables (0:16:26)

**Key Concepts**:

**Type is Everything - Type is Life**:

- Type provides two critical pieces of information: size (memory footprint) and representation (what the data means)
- Without type information, you cannot determine the value of a bit pattern
- The same bit pattern can represent infinite different values depending on type
- Understanding type is essential for understanding cost and performance

**Built-in Types and Memory Layout**:

- Numeric types: int8, int16, int32, int64, uint8, uint16, uint32, uint64
- Architecture-dependent types: int, uint, uintptr (size depends on platform)
- On AMD64: int, uint, uintptr are 64-bit (8 bytes)
- On 32-bit systems: int, uint, uintptr are 32-bit (4 bytes)
- Floating-point: float32 (4 bytes), float64 (8 bytes)
- Boolean: bool (1 byte)
- String: 2-word data structure (8-16 bytes depending on architecture)

**Zero Values and Integrity**:

- All allocated memory is initialized to at least its zero value state
- Zero value is an integrity feature that prevents corruption
- Cost of integrity is minimal performance overhead
- Zero values: 0 for numerics, false for bool, "" for string, nil for pointers

**Variable Declaration Patterns**:

- Use `var` when initializing to zero value (readability marker)
- Use `:=` (short variable declaration) when initializing to non-zero value
- `var` guarantees zero value 100% of the time
- Consistency in declaration patterns improves code readability

**String Internal Structure**:

- String is a 2-word data structure: pointer + length
- First word: pointer to backing array of bytes
- Second word: number of bytes
- Empty string: nil pointer, zero length
- String size varies by architecture (8 bytes on 32-bit, 16 bytes on 64-bit)

**Conversion vs Casting**:

- Go has conversion, not casting
- Conversion may incur memory allocation cost but maintains integrity
- Casting (available via unsafe package) can corrupt memory
- Conversion over casting is an integrity play

**Hands-on Exercise 1: Type Analysis and Memory Layout**:

```go
// Type system exploration for automation systems
package main

import (
    "fmt"
    "unsafe"
)

func main() {
    // Demonstrate the principle "Type is Life"
    demonstrateTypeImportance()

    // Explore built-in types and their sizes
    analyzeBuiltinTypes()

    // Practice variable declaration patterns
    practiceDeclarationPatterns()

    // Understand string internal structure
    exploreStringStructure()
}

func demonstrateTypeImportance() {
    fmt.Println("=== Type is Life Demonstration ===")

    // Same bit pattern, different interpretations
    var value uint32 = 1065353216
    fmt.Printf("As uint32: %d\n", value)
    fmt.Printf("As float32: %f\n", *(*float32)(unsafe.Pointer(&value)))
    fmt.Printf("As bytes: %v\n", *(*[4]byte)(unsafe.Pointer(&value)))
    fmt.Println()
}

func analyzeBuiltinTypes() {
    fmt.Println("=== Built-in Types Analysis ===")

    // Numeric types
    var i8 int8
    var i16 int16
    var i32 int32
    var i64 int64
    var ui8 uint8
    var ui16 uint16
    var ui32 uint32
    var ui64 uint64

    // Architecture-dependent types
    var i int
    var ui uint
    var uptr uintptr

    // Floating-point types
    var f32 float32
    var f64 float64

    // Boolean and string
    var b bool
    var s string

    fmt.Printf("int8 size: %d bytes, value: %d\n", unsafe.Sizeof(i8), i8)
    fmt.Printf("int16 size: %d bytes, value: %d\n", unsafe.Sizeof(i16), i16)
    fmt.Printf("int32 size: %d bytes, value: %d\n", unsafe.Sizeof(i32), i32)
    fmt.Printf("int64 size: %d bytes, value: %d\n", unsafe.Sizeof(i64), i64)
    fmt.Printf("uint8 size: %d bytes, value: %d\n", unsafe.Sizeof(ui8), ui8)
    fmt.Printf("uint16 size: %d bytes, value: %d\n", unsafe.Sizeof(ui16), ui16)
    fmt.Printf("uint32 size: %d bytes, value: %d\n", unsafe.Sizeof(ui32), ui32)
    fmt.Printf("uint64 size: %d bytes, value: %d\n", unsafe.Sizeof(ui64), ui64)

    fmt.Printf("int size: %d bytes, value: %d (architecture-dependent)\n", unsafe.Sizeof(i), i)
    fmt.Printf("uint size: %d bytes, value: %d (architecture-dependent)\n", unsafe.Sizeof(ui), ui)
    fmt.Printf("uintptr size: %d bytes, value: %d (architecture-dependent)\n", unsafe.Sizeof(uptr), uptr)

    fmt.Printf("float32 size: %d bytes, value: %f\n", unsafe.Sizeof(f32), f32)
    fmt.Printf("float64 size: %d bytes, value: %f\n", unsafe.Sizeof(f64), f64)

    fmt.Printf("bool size: %d bytes, value: %t\n", unsafe.Sizeof(b), b)
    fmt.Printf("string size: %d bytes, value: %q\n", unsafe.Sizeof(s), s)
    fmt.Println()
}

func practiceDeclarationPatterns() {
    fmt.Println("=== Variable Declaration Patterns ===")

    // Use var for zero value initialization
    var serverPort int        // zero value: 0
    var databaseURL string    // zero value: ""
    var enableLogging bool    // zero value: false

    fmt.Printf("Zero values - Port: %d, URL: %q, Logging: %t\n",
        serverPort, databaseURL, enableLogging)

    // Use := for non-zero value initialization
    activePort := 8080
    activeURL := "localhost:5432"
    activeLogging := true

    fmt.Printf("Active values - Port: %d, URL: %q, Logging: %t\n",
        activePort, activeURL, activeLogging)

    // Demonstrate conversion
    var precisePort int32 = 8080
    var genericPort int = int(precisePort) // explicit conversion required

    fmt.Printf("Converted port from int32 to int: %d\n", genericPort)
    fmt.Println()
}

func exploreStringStructure() {
    fmt.Println("=== String Internal Structure ===")

    var emptyString string
    helloString := "Hello, Automation!"

    fmt.Printf("Empty string size: %d bytes\n", unsafe.Sizeof(emptyString))
    fmt.Printf("Hello string size: %d bytes (same as empty - it's the header)\n", unsafe.Sizeof(helloString))
    fmt.Printf("Hello string length: %d bytes\n", len(helloString))
    fmt.Printf("Hello string content: %q\n", helloString)

    // Demonstrate string is 2-word structure
    type stringHeader struct {
        data uintptr
        len  int
    }

    header := (*stringHeader)(unsafe.Pointer(&helloString))
    fmt.Printf("String header - Data pointer: %x, Length: %d\n", header.data, header.len)
}
```

**Hands-on Exercise 2: Automation Configuration System**:

```go
// Configuration management system demonstrating type system
package main

import (
    "fmt"
    "time"
)

// Configuration types for automation system
type DatabaseConfig struct {
    Host     string
    Port     int
    Username string
    Password string
    Timeout  time.Duration
}

type ServerConfig struct {
    ListenPort    int
    MaxConnections int
    EnableTLS     bool
    CertPath      string
}

type AutomationConfig struct {
    Database DatabaseConfig
    Server   ServerConfig
    LogLevel string
    Debug    bool
}

func main() {
    // Demonstrate zero value initialization
    var defaultConfig AutomationConfig
    fmt.Println("=== Default Configuration (Zero Values) ===")
    displayConfig("Default", defaultConfig)

    // Demonstrate non-zero value initialization
    productionConfig := AutomationConfig{
        Database: DatabaseConfig{
            Host:     "prod-db.company.com",
            Port:     5432,
            Username: "automation_user",
            Password: "secure_password",
            Timeout:  30 * time.Second,
        },
        Server: ServerConfig{
            ListenPort:     8080,
            MaxConnections: 1000,
            EnableTLS:      true,
            CertPath:       "/etc/ssl/certs/automation.pem",
        },
        LogLevel: "INFO",
        Debug:    false,
    }

    fmt.Println("\n=== Production Configuration ===")
    displayConfig("Production", productionConfig)

    // Demonstrate type conversion in configuration
    demonstrateTypeConversion()
}

func displayConfig(name string, config AutomationConfig) {
    fmt.Printf("%s Config:\n", name)
    fmt.Printf("  Database: %s:%d (timeout: %v)\n",
        config.Database.Host, config.Database.Port, config.Database.Timeout)
    fmt.Printf("  Server: port %d, max connections %d, TLS: %t\n",
        config.Server.ListenPort, config.Server.MaxConnections, config.Server.EnableTLS)
    fmt.Printf("  Logging: level %s, debug: %t\n", config.LogLevel, config.Debug)
}

func demonstrateTypeConversion() {
    fmt.Println("\n=== Type Conversion in Configuration ===")

    // Configuration values might come as different types
    var portString = "8080"
    var timeoutString = "30"

    // Convert string to int (would need strconv in real code)
    // This is a simplified example
    var port int = 8080  // In reality: strconv.Atoi(portString)
    var timeout int = 30 // In reality: strconv.Atoi(timeoutString)

    // Convert to appropriate types for configuration
    var serverPort int = port
    var timeoutDuration time.Duration = time.Duration(timeout) * time.Second

    fmt.Printf("Converted port: %d (from string %q)\n", serverPort, portString)
    fmt.Printf("Converted timeout: %v (from string %q)\n", timeoutDuration, timeoutString)
}
```

**Hands-on Exercise 3: Memory Cost Analysis**:

```go
// Memory cost analysis for automation data structures
package main

import (
    "fmt"
    "unsafe"
)

type SmallConfig struct {
    Enabled bool
    Port    int16
}

type LargeConfig struct {
    Enabled     bool
    Port        int64
    Description string
    Tags        []string
    Metadata    map[string]interface{}
}

func main() {
    fmt.Println("=== Memory Cost Analysis ===")

    // Analyze small vs large structures
    var small SmallConfig
    var large LargeConfig

    fmt.Printf("SmallConfig size: %d bytes\n", unsafe.Sizeof(small))
    fmt.Printf("LargeConfig size: %d bytes\n", unsafe.Sizeof(large))

    // Demonstrate cost of different types
    analyzeCostOfTypes()

    // Show impact of type choices on memory usage
    demonstrateTypeChoiceImpact()
}

func analyzeCostOfTypes() {
    fmt.Println("\n=== Cost of Different Types ===")

    var b bool
    var i8 int8
    var i16 int16
    var i32 int32
    var i64 int64
    var f32 float32
    var f64 float64
    var s string
    var slice []int
    var m map[string]int

    fmt.Printf("bool: %d bytes\n", unsafe.Sizeof(b))
    fmt.Printf("int8: %d bytes\n", unsafe.Sizeof(i8))
    fmt.Printf("int16: %d bytes\n", unsafe.Sizeof(i16))
    fmt.Printf("int32: %d bytes\n", unsafe.Sizeof(i32))
    fmt.Printf("int64: %d bytes\n", unsafe.Sizeof(i64))
    fmt.Printf("float32: %d bytes\n", unsafe.Sizeof(f32))
    fmt.Printf("float64: %d bytes\n", unsafe.Sizeof(f64))
    fmt.Printf("string: %d bytes (header only)\n", unsafe.Sizeof(s))
    fmt.Printf("slice: %d bytes (header only)\n", unsafe.Sizeof(slice))
    fmt.Printf("map: %d bytes (header only)\n", unsafe.Sizeof(m))
}

func demonstrateTypeChoiceImpact() {
    fmt.Println("\n=== Impact of Type Choices ===")

    // Scenario: storing 1000 port numbers
    const count = 1000

    // Using int64 (8 bytes each)
    ports64 := make([]int64, count)
    fmt.Printf("1000 ports as int64: %d bytes\n", unsafe.Sizeof(ports64[0]) * count)

    // Using int16 (2 bytes each) - sufficient for port numbers
    ports16 := make([]int16, count)
    fmt.Printf("1000 ports as int16: %d bytes\n", unsafe.Sizeof(ports16[0]) * count)

    savings := (unsafe.Sizeof(ports64[0]) - unsafe.Sizeof(ports16[0])) * count
    fmt.Printf("Memory savings: %d bytes (%.1f%% reduction)\n",
        savings, float64(savings)/float64(unsafe.Sizeof(ports64[0]) * count) * 100)
}
```

**Prerequisites**: Session 1

---

### Session 3: Struct Types and Memory Layout

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master user-defined struct types as Go's primary data organization mechanism
- Understand memory layout, alignment, and padding in structs
- Learn the critical difference between named and literal (anonymous) types
- Understand Go's stance on implicit vs explicit conversion
- Apply struct design principles to automation and integration scenarios
- Optimize for correctness first, performance second

**Videos Covered**:

- 2.3 Struct Types (0:23:27)

**Key Concepts**:

**User-Defined Types and Structs**:

- Structs are Go's primary mechanism for creating composite types
- Go uses `struct` keyword instead of `class` - focuses on data, not behavior
- Struct types define the shape and layout of data in memory
- Construction tells you nothing about cost - sharing tells you everything

**Memory Layout and Alignment**:

- Hardware requires data alignment for efficient memory access
- Alignment prevents values from crossing word boundaries
- Padding is inserted to maintain proper alignment
- 1-byte values (bool): can be placed anywhere
- 2-byte values (int16): must be 2-byte aligned (addresses 0, 2, 4, 6...)
- 4-byte values (int32, float32): must be 4-byte aligned (addresses 0, 4, 8...)
- 8-byte values (int64, float64): must be 8-byte aligned (addresses 0, 8, 16...)

**Struct Size Calculation**:

- Struct size = sum of field sizes + padding
- Padding is added between fields and at the end for alignment
- Struct alignment is determined by its largest field
- Understanding struct size is crucial for understanding memory cost

**Named vs Literal Types**:

- Named types: explicitly declared with `type` keyword
- Literal types: anonymous, defined inline
- Named types have no implicit conversion - explicit conversion required
- Literal types allow more flexible assignment compatibility
- This design prevents accidental type mixing and improves code safety

**Optimization Philosophy**:

- Always optimize for correctness first
- Don't prematurely optimize struct layout for memory
- Only reorder fields for memory efficiency after profiling shows it's necessary
- Readability and logical grouping should drive field ordering by default

**Hands-on Exercise 1: Memory Layout and Alignment**:

```go
// Memory layout analysis for automation data structures
package main

import (
    "fmt"
    "unsafe"
)

// Example struct with padding
type AutomationTask struct {
    ID          int64     // 8 bytes, 8-byte aligned
    Name        string    // 16 bytes (2 words), 8-byte aligned
    Priority    int32     // 4 bytes, 4-byte aligned
    Enabled     bool      // 1 byte, 1-byte aligned
    RetryCount  int16     // 2 bytes, 2-byte aligned
    Timeout     float64   // 8 bytes, 8-byte aligned
}

// Memory-optimized version (largest to smallest)
type OptimizedTask struct {
    ID         int64     // 8 bytes
    Timeout    float64   // 8 bytes
    Name       string    // 16 bytes
    Priority   int32     // 4 bytes
    RetryCount int16     // 2 bytes
    Enabled    bool      // 1 byte
    // 1 byte padding at end for 8-byte struct alignment
}

// Poor layout example
type PoorTask struct {
    Enabled    bool      // 1 byte
    // 7 bytes padding
    ID         int64     // 8 bytes
    Priority   int32     // 4 bytes
    // 4 bytes padding
    Timeout    float64   // 8 bytes
    RetryCount int16     // 2 bytes
    // 6 bytes padding
    Name       string    // 16 bytes
}

func main() {
    analyzeStructSizes()
    demonstrateAlignment()
    showFieldOffsets()
}

func analyzeStructSizes() {
    fmt.Println("=== Struct Size Analysis ===")

    var regular AutomationTask
    var optimized OptimizedTask
    var poor PoorTask

    fmt.Printf("AutomationTask size: %d bytes\n", unsafe.Sizeof(regular))
    fmt.Printf("OptimizedTask size: %d bytes\n", unsafe.Sizeof(optimized))
    fmt.Printf("PoorTask size: %d bytes\n", unsafe.Sizeof(poor))

    // Calculate theoretical minimum (sum of field sizes)
    theoreticalMin := unsafe.Sizeof(int64(0)) + unsafe.Sizeof("") +
                     unsafe.Sizeof(int32(0)) + unsafe.Sizeof(bool(false)) +
                     unsafe.Sizeof(int16(0)) + unsafe.Sizeof(float64(0))

    fmt.Printf("Theoretical minimum: %d bytes\n", theoreticalMin)
    fmt.Printf("Regular padding: %d bytes\n", unsafe.Sizeof(regular) - theoreticalMin)
    fmt.Printf("Poor padding: %d bytes\n", unsafe.Sizeof(poor) - theoreticalMin)
    fmt.Println()
}

func demonstrateAlignment() {
    fmt.Println("=== Alignment Requirements ===")

    var b bool
    var i16 int16
    var i32 int32
    var i64 int64
    var f32 float32
    var f64 float64
    var s string

    fmt.Printf("bool alignment: %d bytes\n", unsafe.Alignof(b))
    fmt.Printf("int16 alignment: %d bytes\n", unsafe.Alignof(i16))
    fmt.Printf("int32 alignment: %d bytes\n", unsafe.Alignof(i32))
    fmt.Printf("int64 alignment: %d bytes\n", unsafe.Alignof(i64))
    fmt.Printf("float32 alignment: %d bytes\n", unsafe.Alignof(f32))
    fmt.Printf("float64 alignment: %d bytes\n", unsafe.Alignof(f64))
    fmt.Printf("string alignment: %d bytes\n", unsafe.Alignof(s))
    fmt.Println()
}

func showFieldOffsets() {
    fmt.Println("=== Field Offsets in AutomationTask ===")

    var task AutomationTask

    fmt.Printf("ID offset: %d\n", unsafe.Offsetof(task.ID))
    fmt.Printf("Name offset: %d\n", unsafe.Offsetof(task.Name))
    fmt.Printf("Priority offset: %d\n", unsafe.Offsetof(task.Priority))
    fmt.Printf("Enabled offset: %d\n", unsafe.Offsetof(task.Enabled))
    fmt.Printf("RetryCount offset: %d\n", unsafe.Offsetof(task.RetryCount))
    fmt.Printf("Timeout offset: %d\n", unsafe.Offsetof(task.Timeout))
    fmt.Println()
}
```

**Hands-on Exercise 2: Named vs Literal Types**:

```go
// Demonstration of named vs literal types and conversion rules
package main

import "fmt"

// Named types for automation system
type ServiceID string
type TaskID string

// Both have identical underlying types but are different named types
type DatabaseConfig struct {
    Host     string
    Port     int
    Username string
    Password string
}

type CacheConfig struct {
    Host     string
    Port     int
    Username string
    Password string
}

func main() {
    demonstrateNamedTypeConversion()
    demonstrateLiteralTypeFlexibility()
    showImplicitConversionDanger()
}

func demonstrateNamedTypeConversion() {
    fmt.Println("=== Named Type Conversion ===")

    // Named types require explicit conversion even when identical
    var serviceID ServiceID = "web-service"
    var taskID TaskID = "task-001"

    fmt.Printf("ServiceID: %s\n", serviceID)
    fmt.Printf("TaskID: %s\n", taskID)

    // This would cause a compile error:
    // taskID = serviceID  // Cannot use serviceID (type ServiceID) as type TaskID

    // Explicit conversion is required:
    taskID = TaskID(serviceID)
    fmt.Printf("Converted TaskID: %s\n", taskID)

    // Same with struct types
    dbConfig := DatabaseConfig{
        Host:     "localhost",
        Port:     5432,
        Username: "admin",
        Password: "secret",
    }

    var cacheConfig CacheConfig
    // This would cause a compile error:
    // cacheConfig = dbConfig  // Cannot use dbConfig (type DatabaseConfig) as type CacheConfig

    // Explicit conversion required:
    cacheConfig = CacheConfig(dbConfig)
    fmt.Printf("Converted cache config: %+v\n", cacheConfig)
    fmt.Println()
}

func demonstrateLiteralTypeFlexibility() {
    fmt.Println("=== Literal Type Flexibility ===")

    // Literal (anonymous) struct
    config1 := struct {
        Host string
        Port int
    }{
        Host: "localhost",
        Port: 8080,
    }

    // Another variable with the same literal type structure
    var config2 struct {
        Host string
        Port int
    }

    // This assignment works - both are literal types with same structure
    config2 = config1
    fmt.Printf("Config1: %+v\n", config1)
    fmt.Printf("Config2: %+v\n", config2)

    // Can also assign to named type from literal type
    var dbConfig DatabaseConfig
    literalConfig := struct {
        Host     string
        Port     int
        Username string
        Password string
    }{
        Host:     "db.example.com",
        Port:     5432,
        Username: "user",
        Password: "pass",
    }

    // This works - literal to named type assignment is allowed
    dbConfig = DatabaseConfig(literalConfig)
    fmt.Printf("DB Config from literal: %+v\n", dbConfig)
    fmt.Println()
}

func showImplicitConversionDanger() {
    fmt.Println("=== Why No Implicit Conversion? ===")

    // This demonstrates why implicit conversion can be dangerous
    type UserID int
    type ProductID int

    var userID UserID = 12345
    var productID ProductID = 67890

    fmt.Printf("UserID: %d\n", userID)
    fmt.Printf("ProductID: %d\n", productID)

    // If Go allowed implicit conversion, this bug would be possible:
    // processUser(productID)  // Accidentally passing product ID as user ID!

    // With explicit conversion, the intent is clear:
    processUser(UserID(productID))  // Clearly intentional conversion

    fmt.Println("Explicit conversion prevents accidental type mixing")
}

func processUser(id UserID) {
    fmt.Printf("Processing user with ID: %d\n", id)
}
```

**Hands-on Exercise 3: Automation System Design**:

```go
// Comprehensive automation system using proper struct design
package main

import (
    "encoding/json"
    "fmt"
    "time"
)

// Core automation types
type TaskStatus int

const (
    TaskPending TaskStatus = iota
    TaskRunning
    TaskCompleted
    TaskFailed
    TaskCancelled
)

func (ts TaskStatus) String() string {
    switch ts {
    case TaskPending:
        return "Pending"
    case TaskRunning:
        return "Running"
    case TaskCompleted:
        return "Completed"
    case TaskFailed:
        return "Failed"
    case TaskCancelled:
        return "Cancelled"
    default:
        return "Unknown"
    }
}

// Well-designed struct for automation tasks
type AutomationTask struct {
    // Group related fields together for readability
    ID          string    `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description,omitempty"`

    // Status and timing information
    Status      TaskStatus `json:"status"`
    CreatedAt   time.Time  `json:"created_at"`
    StartedAt   *time.Time `json:"started_at,omitempty"`
    CompletedAt *time.Time `json:"completed_at,omitempty"`

    // Configuration
    Priority    int           `json:"priority"`
    MaxRetries  int           `json:"max_retries"`
    Timeout     time.Duration `json:"timeout"`

    // Runtime data
    CurrentRetry int                    `json:"current_retry"`
    LastError    string                 `json:"last_error,omitempty"`
    Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// API response structure
type TaskResponse struct {
    Success bool            `json:"success"`
    Message string          `json:"message"`
    Task    *AutomationTask `json:"task,omitempty"`
    Error   string          `json:"error,omitempty"`
}

// Batch operation structure
type BatchOperation struct {
    OperationID string           `json:"operation_id"`
    Tasks       []AutomationTask `json:"tasks"`
    TotalCount  int              `json:"total_count"`
    Completed   int              `json:"completed"`
    Failed      int              `json:"failed"`
    StartTime   time.Time        `json:"start_time"`
}

func main() {
    demonstrateStructUsage()
    showJSONSerialization()
    analyzeBatchOperations()
}

func demonstrateStructUsage() {
    fmt.Println("=== Automation Task Management ===")

    // Create a new task using literal construction
    task := AutomationTask{
        ID:          "task-001",
        Name:        "Database Backup",
        Description: "Daily backup of production database",
        Status:      TaskPending,
        CreatedAt:   time.Now(),
        Priority:    5,
        MaxRetries:  3,
        Timeout:     30 * time.Minute,
        Metadata: map[string]interface{}{
            "database": "production",
            "type":     "full_backup",
        },
    }

    fmt.Printf("Created task: %s\n", task.Name)
    fmt.Printf("Status: %s\n", task.Status)
    fmt.Printf("Priority: %d\n", task.Priority)
    fmt.Printf("Timeout: %v\n", task.Timeout)

    // Simulate task execution
    startTask(&task)
    completeTask(&task)

    fmt.Printf("Final status: %s\n", task.Status)
    if task.CompletedAt != nil {
        duration := task.CompletedAt.Sub(*task.StartedAt)
        fmt.Printf("Execution time: %v\n", duration)
    }
    fmt.Println()
}

func startTask(task *AutomationTask) {
    now := time.Now()
    task.Status = TaskRunning
    task.StartedAt = &now
}

func completeTask(task *AutomationTask) {
    now := time.Now()
    task.Status = TaskCompleted
    task.CompletedAt = &now
}

func showJSONSerialization() {
    fmt.Println("=== JSON Serialization ===")

    task := AutomationTask{
        ID:          "task-002",
        Name:        "Log Rotation",
        Description: "Rotate application logs",
        Status:      TaskCompleted,
        CreatedAt:   time.Now().Add(-1 * time.Hour),
        Priority:    3,
        MaxRetries:  2,
        Timeout:     10 * time.Minute,
    }

    now := time.Now()
    task.StartedAt = &now
    task.CompletedAt = &now

    response := TaskResponse{
        Success: true,
        Message: "Task completed successfully",
        Task:    &task,
    }

    jsonData, err := json.MarshalIndent(response, "", "  ")
    if err != nil {
        fmt.Printf("JSON marshaling error: %v\n", err)
        return
    }

    fmt.Printf("Task Response JSON:\n%s\n\n", jsonData)
}

func analyzeBatchOperations() {
    fmt.Println("=== Batch Operations ===")

    // Create multiple tasks
    tasks := []AutomationTask{
        {
            ID:         "batch-001",
            Name:       "Cleanup Temp Files",
            Status:     TaskCompleted,
            CreatedAt:  time.Now().Add(-30 * time.Minute),
            Priority:   2,
            MaxRetries: 1,
            Timeout:    5 * time.Minute,
        },
        {
            ID:         "batch-002",
            Name:       "Update Configuration",
            Status:     TaskFailed,
            CreatedAt:  time.Now().Add(-25 * time.Minute),
            Priority:   4,
            MaxRetries: 3,
            Timeout:    15 * time.Minute,
            LastError:  "Configuration file not found",
        },
        {
            ID:         "batch-003",
            Name:       "Send Notifications",
            Status:     TaskRunning,
            CreatedAt:  time.Now().Add(-10 * time.Minute),
            Priority:   1,
            MaxRetries: 2,
            Timeout:    2 * time.Minute,
        },
    }

    batch := BatchOperation{
        OperationID: "batch-op-001",
        Tasks:       tasks,
        TotalCount:  len(tasks),
        StartTime:   time.Now().Add(-30 * time.Minute),
    }

    // Calculate statistics
    for _, task := range batch.Tasks {
        switch task.Status {
        case TaskCompleted:
            batch.Completed++
        case TaskFailed:
            batch.Failed++
        }
    }

    fmt.Printf("Batch Operation: %s\n", batch.OperationID)
    fmt.Printf("Total tasks: %d\n", batch.TotalCount)
    fmt.Printf("Completed: %d\n", batch.Completed)
    fmt.Printf("Failed: %d\n", batch.Failed)
    fmt.Printf("Running: %d\n", batch.TotalCount - batch.Completed - batch.Failed)

    // Show memory usage
    fmt.Printf("\nMemory Analysis:\n")
    fmt.Printf("Single task size: %d bytes\n", unsafe.Sizeof(AutomationTask{}))
    fmt.Printf("Batch operation size: %d bytes\n", unsafe.Sizeof(batch))
    fmt.Printf("Total tasks memory: %d bytes\n",
        unsafe.Sizeof(AutomationTask{}) * uintptr(len(tasks)))
}
```

**Prerequisites**: Session 2

---

### Session 4: Pointers Part 1: Pass by Value and Sharing Data

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master the fundamental principle that everything in Go is pass-by-value
- Understand pointers as the mechanism for sharing data across program boundaries
- Learn the critical difference between value and pointer semantics
- Master pointer syntax and safe dereferencing techniques
- Apply pointer concepts effectively in automation data structures
- Understand the cost implications of copying vs sharing data

**Videos Covered**:

- 2.4 Pointers Part 1 (Pass by Value) (0:15:45)
- 2.5 Pointer Part 2 (Sharing Data) (0:10:35)

**Key Concepts**:

**Pass-by-Value Fundamentals**:

- Everything in Go is pass-by-value - no exceptions
- When you pass a value, you get a copy of that value
- The copy is placed in the frame of the receiving function
- Changes to the copy do not affect the original value
- This applies to all types: integers, strings, structs, arrays, slices, maps, channels, functions

**Pointers Enable Data Sharing**:

- Pointers are the only way to share data across program boundaries
- A pointer is a variable that stores the address of another variable
- Pointer syntax: `*T` is a pointer to type T, `&` gets address, `*` dereferences
- When you pass a pointer, you pass a copy of the address
- Both the original and copy point to the same memory location

**Value vs Pointer Semantics**:

- Value semantics: operations work on copies, no side effects
- Pointer semantics: operations work on shared data, side effects possible
- Choose based on whether you want to share or isolate data
- Value semantics are safer but may be less efficient for large data
- Pointer semantics enable mutation but require careful consideration

**Cost Considerations**:

- Copying small values (int, bool, small structs) is often cheaper than pointer indirection
- Copying large values (big structs, arrays) is expensive - use pointers
- Pointer dereferencing has a small CPU cost but enables sharing
- Consider both memory and CPU costs when choosing semantics

**Pointer Safety**:

- Go prevents pointer arithmetic for safety
- Always check for nil pointers before dereferencing
- Use defensive programming with pointer parameters
- Understand that nil pointer dereference causes panic

**Hands-on Exercise 1: Pass-by-Value vs Pointer Sharing**:

```go
// Comprehensive demonstration of value vs pointer semantics
package main

import (
    "fmt"
    "unsafe"
)

type AutomationConfig struct {
    ServiceName string
    Port        int
    Enabled     bool
    Retries     int
    Tags        []string
}

func main() {
    demonstratePassByValue()
    demonstratePointerSharing()
    analyzeCostImplications()
    showPointerSafety()
}

func demonstratePassByValue() {
    fmt.Println("=== Pass-by-Value Demonstration ===")

    config := AutomationConfig{
        ServiceName: "WebService",
        Port:        8080,
        Enabled:     true,
        Retries:     3,
        Tags:        []string{"web", "api"},
    }

    fmt.Printf("Original config: %+v\n", config)
    fmt.Printf("Original address: %p\n", &config)

    // Pass by value - function gets a copy
    modifyConfigByValue(config)
    fmt.Printf("After value modification: %+v\n", config)

    // Pass by pointer - function gets copy of address
    modifyConfigByPointer(&config)
    fmt.Printf("After pointer modification: %+v\n", config)
    fmt.Println()
}

func modifyConfigByValue(cfg AutomationConfig) {
    fmt.Printf("Inside value function - address: %p\n", &cfg)
    cfg.Port = 9090
    cfg.Enabled = false
    cfg.Tags = append(cfg.Tags, "modified")
    fmt.Printf("Inside value function - modified: %+v\n", cfg)
}

func modifyConfigByPointer(cfg *AutomationConfig) {
    fmt.Printf("Inside pointer function - pointer address: %p\n", &cfg)
    fmt.Printf("Inside pointer function - pointed-to address: %p\n", cfg)
    cfg.Port = 9090
    cfg.Enabled = false
    cfg.Tags = append(cfg.Tags, "modified")
    fmt.Printf("Inside pointer function - modified: %+v\n", *cfg)
}

func demonstratePointerSharing() {
    fmt.Println("=== Pointer Sharing Demonstration ===")

    config := &AutomationConfig{
        ServiceName: "DatabaseService",
        Port:        5432,
        Enabled:     true,
        Retries:     5,
    }

    fmt.Printf("Original config: %+v\n", *config)

    // Multiple pointers to the same data
    configPtr1 := config
    configPtr2 := config

    fmt.Printf("Pointer 1 address: %p, points to: %p\n", &configPtr1, configPtr1)
    fmt.Printf("Pointer 2 address: %p, points to: %p\n", &configPtr2, configPtr2)

    // Modify through one pointer
    configPtr1.Port = 3306

    // All pointers see the change
    fmt.Printf("After modification through pointer 1:\n")
    fmt.Printf("  Original: %+v\n", *config)
    fmt.Printf("  Pointer 1: %+v\n", *configPtr1)
    fmt.Printf("  Pointer 2: %+v\n", *configPtr2)
    fmt.Println()
}

func analyzeCostImplications() {
    fmt.Println("=== Cost Analysis: Value vs Pointer ===")

    // Small struct - value semantics might be better
    type SmallConfig struct {
        Port    int
        Enabled bool
    }

    // Large struct - pointer semantics likely better
    type LargeConfig struct {
        ServiceName string
        Port        int
        Enabled     bool
        Retries     int
        Tags        []string
        Metadata    map[string]interface{}
        Buffer      [1024]byte // Large array
    }

    var small SmallConfig
    var large LargeConfig
    var smallPtr *SmallConfig
    var largePtr *LargeConfig

    fmt.Printf("SmallConfig size: %d bytes\n", unsafe.Sizeof(small))
    fmt.Printf("LargeConfig size: %d bytes\n", unsafe.Sizeof(large))
    fmt.Printf("Pointer size: %d bytes\n", unsafe.Sizeof(smallPtr))
    fmt.Printf("Pointer size: %d bytes\n", unsafe.Sizeof(largePtr))

    fmt.Printf("\nCost analysis:\n")
    fmt.Printf("- Copying SmallConfig: %d bytes\n", unsafe.Sizeof(small))
    fmt.Printf("- Using pointer to SmallConfig: %d bytes + indirection cost\n", unsafe.Sizeof(smallPtr))
    fmt.Printf("- Copying LargeConfig: %d bytes (expensive!)\n", unsafe.Sizeof(large))
    fmt.Printf("- Using pointer to LargeConfig: %d bytes + indirection cost\n", unsafe.Sizeof(largePtr))
    fmt.Println()
}

func showPointerSafety() {
    fmt.Println("=== Pointer Safety ===")

    var config *AutomationConfig

    // Check for nil before dereferencing
    if config == nil {
        fmt.Println("Config pointer is nil - safe check prevented panic")
    }

    // Initialize pointer
    config = &AutomationConfig{
        ServiceName: "SafeService",
        Port:        8080,
    }

    // Safe to dereference now
    fmt.Printf("Safe dereference: %+v\n", *config)

    // Demonstrate safe pointer usage pattern
    updateConfigSafely(config, 9090)
    updateConfigSafely(nil, 9090) // Won't panic

    fmt.Printf("Final config: %+v\n", *config)
}

func updateConfigSafely(cfg *AutomationConfig, newPort int) {
    if cfg == nil {
        fmt.Println("Cannot update nil config - safely handled")
        return
    }

    cfg.Port = newPort
    fmt.Printf("Updated config port to: %d\n", cfg.Port)
}
```

**Hands-on Exercise 2: Method Receivers - Value vs Pointer**:

```go
// Method receivers demonstrating value vs pointer semantics
package main

import "fmt"

type DatabaseConfig struct {
    Host     string
    Port     int
    Username string
    Password string
    Connected bool
}

// Value receiver - works with a copy
func (d DatabaseConfig) GetConnectionString() string {
    return fmt.Sprintf("%s:%s@%s:%d", d.Username, d.Password, d.Host, d.Port)
}

// Value receiver - cannot modify original
func (d DatabaseConfig) AttemptConnect() bool {
    // This modification only affects the copy
    d.Connected = true
    fmt.Printf("Inside AttemptConnect (value): Connected = %t\n", d.Connected)
    return true
}

// Pointer receiver - works with original
func (d *DatabaseConfig) UpdatePassword(newPassword string) {
    if d == nil {
        fmt.Println("Cannot update password on nil config")
        return
    }
    d.Password = newPassword
}

// Pointer receiver - can modify original
func (d *DatabaseConfig) Connect() bool {
    if d == nil {
        return false
    }
    d.Connected = true
    fmt.Printf("Inside Connect (pointer): Connected = %t\n", d.Connected)
    return true
}

// Pointer receiver for consistency (even though it doesn't modify)
func (d *DatabaseConfig) IsConnected() bool {
    if d == nil {
        return false
    }
    return d.Connected
}

func main() {
    demonstrateMethodReceivers()
    showReceiverConsistency()
}

func demonstrateMethodReceivers() {
    fmt.Println("=== Method Receivers: Value vs Pointer ===")

    config := DatabaseConfig{
        Host:     "localhost",
        Port:     5432,
        Username: "admin",
        Password: "oldpass",
        Connected: false,
    }

    fmt.Printf("Initial config: %+v\n", config)

    // Value receiver method
    connStr := config.GetConnectionString()
    fmt.Printf("Connection string: %s\n", connStr)

    // Value receiver trying to modify (won't work)
    config.AttemptConnect()
    fmt.Printf("After AttemptConnect: Connected = %t\n", config.Connected)

    // Pointer receiver modifying original
    config.UpdatePassword("newpass")
    fmt.Printf("After UpdatePassword: %+v\n", config)

    // Pointer receiver modifying original
    config.Connect()
    fmt.Printf("After Connect: Connected = %t\n", config.Connected)

    // Check connection status
    fmt.Printf("Is connected: %t\n", config.IsConnected())
    fmt.Println()
}

func showReceiverConsistency() {
    fmt.Println("=== Receiver Consistency Guidelines ===")

    // When to use value receivers:
    fmt.Println("Use VALUE receivers when:")
    fmt.Println("- Method doesn't modify the receiver")
    fmt.Println("- Receiver is small (few fields)")
    fmt.Println("- Receiver is a basic type or small struct")
    fmt.Println("- You want to prevent accidental modifications")

    fmt.Println("\nUse POINTER receivers when:")
    fmt.Println("- Method modifies the receiver")
    fmt.Println("- Receiver is large (expensive to copy)")
    fmt.Println("- You want consistency across all methods")
    fmt.Println("- Receiver contains sync.Mutex or similar")

    // Demonstrate mixed usage (generally not recommended)
    config := &DatabaseConfig{
        Host:     "example.com",
        Port:     5432,
        Username: "user",
        Password: "pass",
    }

    // Go allows calling value receiver methods on pointers
    connStr := config.GetConnectionString() // Value receiver called on pointer
    fmt.Printf("\nConnection string from pointer: %s\n", connStr)

    // Go allows calling pointer receiver methods on values
    configValue := *config
    configValue.UpdatePassword("newpass") // Pointer receiver called on value
    fmt.Printf("Original config after value method call: %+v\n", *config)
    fmt.Printf("Value config after method call: %+v\n", configValue)
}
```

**Prerequisites**: Session 3

---

### Session 5: Pointers Part 2: Escape Analysis and Memory Management

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master escape analysis and its critical impact on performance
- Understand stack vs heap allocation decisions and their implications
- Learn to use Go tools to analyze memory allocation patterns
- Understand stack growth mechanics and goroutine memory management
- Apply memory-efficient patterns in automation applications
- Learn to write GC-friendly code for long-running processes

**Videos Covered**:

- 2.6 Pointers Part 3 (Escape Analysis) (0:20:20)
- 2.7 Pointers Part 4 (Stack Growth) (0:07:32)
- 2.8 Pointers Part 5 (Garbage Collection) (0:06:32)

**Key Concepts**:

**Escape Analysis Fundamentals**:

- Compiler analysis that determines if a value escapes its function scope
- Escaping means the value needs to survive beyond the function's lifetime
- Values that don't escape can be allocated on the stack (fast)
- Values that escape must be allocated on the heap (slower, GC pressure)
- Use `go build -gcflags="-m"` to see escape analysis decisions

**Common Escape Scenarios**:

- Returning pointers to local variables
- Storing values in heap-allocated data structures
- Passing values to interface{} parameters
- Values too large for the stack
- Closures that capture local variables

**Stack vs Heap Characteristics**:

- Stack: fast allocation/deallocation, automatic cleanup, limited size per goroutine
- Heap: slower allocation/deallocation, garbage collected, unlimited size
- Stack allocation is essentially free (just moving stack pointer)
- Heap allocation requires GC and has memory management overhead

**Stack Growth Mechanics**:

- Go uses segmented stacks that can grow and shrink dynamically
- Initial stack size is small (2KB), grows as needed up to 1GB
- Stack growth is handled automatically by the runtime
- Each goroutine has its own stack
- Stack overflow is virtually impossible in Go

**Garbage Collection Impact**:

- Go uses a concurrent, tri-color mark-and-sweep collector
- GC runs concurrently with your program to minimize pauses
- More heap allocations = more GC pressure = potential performance impact
- Understanding allocation patterns helps write GC-friendly code

**Performance Implications**:

- Stack allocation: ~0 cost, automatic cleanup
- Heap allocation: allocation cost + GC cost + cache locality issues
- Reducing heap allocations improves performance in long-running applications
- Profile first, optimize second - don't prematurely optimize

**Hands-on Exercise 1: Escape Analysis Demonstration**:

```go
// Comprehensive escape analysis demonstration
// Run with: go build -gcflags="-m" escape_analysis.go
package main

import (
    "fmt"
    "runtime"
)

type MetricsData struct {
    Timestamp int64
    Value     float64
    Tags      map[string]string
}

func main() {
    demonstrateEscapeScenarios()
    measureAllocationPerformance()
    showStackGrowth()
}

// Scenario 1: Stack allocation - doesn't escape
func createStackMetrics() MetricsData {
    // This stays on stack - returned by value
    return MetricsData{
        Timestamp: 1234567890,
        Value:     42.5,
        Tags:      make(map[string]string), // This map will escape though
    }
}

// Scenario 2: Heap allocation - escapes via return pointer
func createHeapMetrics() *MetricsData {
    // This escapes to heap - pointer returned
    metrics := MetricsData{
        Timestamp: 1234567890,
        Value:     42.5,
        Tags:      make(map[string]string),
    }
    return &metrics // Taking address causes escape
}

// Scenario 3: Escape via interface{}
func processInterface(data interface{}) {
    fmt.Printf("Processing: %v\n", data)
}

// Scenario 4: Escape via slice storage
func storeInSlice(slice *[]*MetricsData) *MetricsData {
    // This will escape because it's stored in heap-allocated slice
    metrics := &MetricsData{
        Timestamp: 1234567890,
        Value:     99.9,
    }
    *slice = append(*slice, metrics)
    return metrics
}

// Scenario 5: Large value that exceeds stack limit
func createLargeValue() [10000]int {
    // This might escape due to size
    var large [10000]int
    for i := range large {
        large[i] = i
    }
    return large
}

func demonstrateEscapeScenarios() {
    fmt.Println("=== Escape Analysis Scenarios ===")
    fmt.Println("Run with: go build -gcflags=\"-m\" to see escape analysis")

    // Stack allocation
    stackMetrics := createStackMetrics()
    fmt.Printf("Stack metrics: %+v\n", stackMetrics)

    // Heap allocation
    heapMetrics := createHeapMetrics()
    fmt.Printf("Heap metrics: %+v\n", *heapMetrics)

    // Interface escape
    value := 42
    processInterface(value) // This will cause 'value' to escape

    // Slice storage escape
    var metricsSlice []*MetricsData
    storedMetrics := storeInSlice(&metricsSlice)
    fmt.Printf("Stored metrics: %+v\n", *storedMetrics)

    // Large value
    largeArray := createLargeValue()
    fmt.Printf("Large array first element: %d\n", largeArray[0])

    fmt.Println()
}

func measureAllocationPerformance() {
    fmt.Println("=== Allocation Performance Comparison ===")

    const iterations = 100000

    // Measure stack allocations
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    for i := 0; i < iterations; i++ {
        _ = createStackMetrics()
    }

    runtime.ReadMemStats(&m2)
    fmt.Printf("Stack allocations - Heap objects: %d -> %d (diff: %d)\n",
        m1.HeapObjects, m2.HeapObjects, m2.HeapObjects-m1.HeapObjects)

    // Measure heap allocations
    runtime.GC()
    runtime.ReadMemStats(&m1)

    for i := 0; i < iterations; i++ {
        _ = createHeapMetrics()
    }

    runtime.ReadMemStats(&m2)
    fmt.Printf("Heap allocations - Heap objects: %d -> %d (diff: %d)\n",
        m1.HeapObjects, m2.HeapObjects, m2.HeapObjects-m1.HeapObjects)

    fmt.Printf("GC cycles during test: %d\n", m2.NumGC-m1.NumGC)
    fmt.Println()
}

func showStackGrowth() {
    fmt.Println("=== Stack Growth Demonstration ===")

    // Recursive function to demonstrate stack growth
    var recursiveFunc func(int, int)
    recursiveFunc = func(depth, maxDepth int) {
        if depth >= maxDepth {
            return
        }

        // Allocate some stack space
        var localArray [100]int
        localArray[0] = depth

        if depth%1000 == 0 {
            fmt.Printf("Recursion depth: %d, local array: %d\n", depth, localArray[0])
        }

        recursiveFunc(depth+1, maxDepth)
    }

    // This will cause stack growth
    recursiveFunc(0, 5000)
    fmt.Println("Stack growth completed successfully")
}
```

**Hands-on Exercise 2: Memory-Efficient Automation System**:

```go
// Memory-efficient automation system design
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

// Efficient task structure - designed to minimize escapes
type Task struct {
    ID       uint64
    Type     uint8
    Priority uint8
    Status   uint8
    Created  int64 // Unix timestamp
}

// Task pool to reuse task objects and reduce allocations
type TaskPool struct {
    pool sync.Pool
}

func NewTaskPool() *TaskPool {
    return &TaskPool{
        pool: sync.Pool{
            New: func() interface{} {
                return &Task{}
            },
        },
    }
}

func (tp *TaskPool) Get() *Task {
    return tp.pool.Get().(*Task)
}

func (tp *TaskPool) Put(task *Task) {
    // Reset task before returning to pool
    *task = Task{}
    tp.pool.Put(task)
}

// Efficient metrics collector - avoids unnecessary allocations
type MetricsCollector struct {
    buffer    []float64 // Pre-allocated buffer
    bufferPos int
    mu        sync.Mutex
}

func NewMetricsCollector(bufferSize int) *MetricsCollector {
    return &MetricsCollector{
        buffer: make([]float64, bufferSize),
    }
}

// Stack-friendly method - doesn't escape
func (mc *MetricsCollector) AddMetric(value float64) bool {
    mc.mu.Lock()
    defer mc.mu.Unlock()

    if mc.bufferPos >= len(mc.buffer) {
        return false // Buffer full
    }

    mc.buffer[mc.bufferPos] = value
    mc.bufferPos++
    return true
}

// Returns by value to avoid escape
func (mc *MetricsCollector) GetStats() (count int, avg float64) {
    mc.mu.Lock()
    defer mc.mu.Unlock()

    if mc.bufferPos == 0 {
        return 0, 0
    }

    var sum float64
    for i := 0; i < mc.bufferPos; i++ {
        sum += mc.buffer[i]
    }

    return mc.bufferPos, sum / float64(mc.bufferPos)
}

// Efficient worker that minimizes heap allocations
type Worker struct {
    id          int
    taskPool    *TaskPool
    metrics     *MetricsCollector
    workBuffer  [256]byte // Stack-allocated work buffer
}

func NewWorker(id int, taskPool *TaskPool, metrics *MetricsCollector) *Worker {
    return &Worker{
        id:       id,
        taskPool: taskPool,
        metrics:  metrics,
    }
}

// Process task without causing escapes
func (w *Worker) ProcessTask(taskID uint64, taskType uint8) time.Duration {
    start := time.Now()

    // Get task from pool (reuse allocation)
    task := w.taskPool.Get()
    defer w.taskPool.Put(task)

    // Initialize task
    task.ID = taskID
    task.Type = taskType
    task.Priority = 5
    task.Status = 1 // Processing
    task.Created = start.Unix()

    // Simulate work using stack buffer
    for i := range w.workBuffer {
        w.workBuffer[i] = byte(i % 256)
    }

    // Simulate processing time
    time.Sleep(time.Microsecond * time.Duration(taskType*10))

    duration := time.Since(start)

    // Record metrics (stack-friendly)
    w.metrics.AddMetric(float64(duration.Nanoseconds()))

    return duration
}

func main() {
    demonstrateMemoryEfficientSystem()
}

func demonstrateMemoryEfficientSystem() {
    fmt.Println("=== Memory-Efficient Automation System ===")

    // Initialize system components
    taskPool := NewTaskPool()
    metrics := NewMetricsCollector(10000)

    // Create workers
    const numWorkers = 4
    workers := make([]*Worker, numWorkers)
    for i := 0; i < numWorkers; i++ {
        workers[i] = NewWorker(i+1, taskPool, metrics)
    }

    // Measure memory before processing
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    fmt.Printf("Before processing - Heap objects: %d, Heap size: %d KB\n",
        m1.HeapObjects, m1.HeapSys/1024)

    // Process many tasks
    const numTasks = 10000
    start := time.Now()

    for i := 0; i < numTasks; i++ {
        worker := workers[i%numWorkers]
        worker.ProcessTask(uint64(i), uint8(i%10))
    }

    processingTime := time.Since(start)

    // Measure memory after processing
    runtime.ReadMemStats(&m2)
    fmt.Printf("After processing - Heap objects: %d, Heap size: %d KB\n",
        m2.HeapObjects, m2.HeapSys/1024)

    // Show results
    count, avgDuration := metrics.GetStats()
    fmt.Printf("\nProcessing Results:\n")
    fmt.Printf("Tasks processed: %d\n", count)
    fmt.Printf("Average task duration: %.2f μs\n", avgDuration/1000)
    fmt.Printf("Total processing time: %v\n", processingTime)
    fmt.Printf("Heap object increase: %d\n", m2.HeapObjects-m1.HeapObjects)
    fmt.Printf("GC cycles: %d\n", m2.NumGC-m1.NumGC)

    // Demonstrate pool efficiency
    demonstratePoolEfficiency(taskPool)
}

func demonstratePoolEfficiency(taskPool *TaskPool) {
    fmt.Println("\n=== Pool Efficiency Demonstration ===")

    const iterations = 100000

    // Without pool - creates new objects each time
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    start := time.Now()
    for i := 0; i < iterations; i++ {
        task := &Task{
            ID:      uint64(i),
            Type:    uint8(i % 10),
            Created: time.Now().Unix(),
        }
        _ = task // Use the task
    }
    withoutPoolTime := time.Since(start)

    runtime.ReadMemStats(&m2)
    fmt.Printf("Without pool - Time: %v, Heap objects: %d -> %d\n",
        withoutPoolTime, m1.HeapObjects, m2.HeapObjects)

    // With pool - reuses objects
    runtime.GC()
    runtime.ReadMemStats(&m1)

    start = time.Now()
    for i := 0; i < iterations; i++ {
        task := taskPool.Get()
        task.ID = uint64(i)
        task.Type = uint8(i % 10)
        task.Created = time.Now().Unix()
        taskPool.Put(task)
    }
    withPoolTime := time.Since(start)

    runtime.ReadMemStats(&m2)
    fmt.Printf("With pool - Time: %v, Heap objects: %d -> %d\n",
        withPoolTime, m1.HeapObjects, m2.HeapObjects)

    fmt.Printf("Pool efficiency: %.2fx faster, %d fewer allocations\n",
        float64(withoutPoolTime)/float64(withPoolTime),
        (m2.HeapObjects-m1.HeapObjects))
}
```

**Hands-on Exercise 3: Escape Analysis Tools and Optimization**:

```go
// Tools and techniques for analyzing and optimizing escape behavior
package main

import (
    "fmt"
    "runtime"
    "time"
)

// Example struct for optimization analysis
type ConfigData struct {
    Name        string
    Value       string
    Timestamp   int64
    Metadata    map[string]interface{}
}

// Version 1: Causes escapes (inefficient)
func processConfigV1(configs []ConfigData) []*ConfigData {
    var results []*ConfigData

    for _, config := range configs {
        // Taking address of loop variable causes escape
        processed := &ConfigData{
            Name:      config.Name + "_processed",
            Value:     config.Value,
            Timestamp: time.Now().Unix(),
            Metadata:  make(map[string]interface{}),
        }
        results = append(results, processed)
    }

    return results
}

// Version 2: Reduces escapes (more efficient)
func processConfigV2(configs []ConfigData) []ConfigData {
    results := make([]ConfigData, 0, len(configs))

    for _, config := range configs {
        // Return by value, no pointer needed
        processed := ConfigData{
            Name:      config.Name + "_processed",
            Value:     config.Value,
            Timestamp: time.Now().Unix(),
            Metadata:  make(map[string]interface{}),
        }
        results = append(results, processed)
    }

    return results
}

// Version 3: Pre-allocated slice (most efficient)
func processConfigV3(configs []ConfigData, results []ConfigData) []ConfigData {
    if cap(results) < len(configs) {
        results = make([]ConfigData, 0, len(configs))
    }
    results = results[:0] // Reset length but keep capacity

    for _, config := range configs {
        processed := ConfigData{
            Name:      config.Name + "_processed",
            Value:     config.Value,
            Timestamp: time.Now().Unix(),
            // Reuse metadata map if possible
            Metadata: config.Metadata,
        }
        results = append(results, processed)
    }

    return results
}

// Benchmark different approaches
func benchmarkApproaches() {
    fmt.Println("=== Benchmarking Different Approaches ===")

    // Create test data
    configs := make([]ConfigData, 1000)
    for i := range configs {
        configs[i] = ConfigData{
            Name:      fmt.Sprintf("config_%d", i),
            Value:     fmt.Sprintf("value_%d", i),
            Timestamp: time.Now().Unix(),
            Metadata:  make(map[string]interface{}),
        }
    }

    const iterations = 100

    // Benchmark V1 (with escapes)
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    start := time.Now()
    for i := 0; i < iterations; i++ {
        _ = processConfigV1(configs)
    }
    v1Time := time.Since(start)

    runtime.ReadMemStats(&m2)
    v1Allocs := m2.HeapObjects - m1.HeapObjects

    // Benchmark V2 (fewer escapes)
    runtime.GC()
    runtime.ReadMemStats(&m1)

    start = time.Now()
    for i := 0; i < iterations; i++ {
        _ = processConfigV2(configs)
    }
    v2Time := time.Since(start)

    runtime.ReadMemStats(&m2)
    v2Allocs := m2.HeapObjects - m1.HeapObjects

    // Benchmark V3 (pre-allocated)
    runtime.GC()
    runtime.ReadMemStats(&m1)

    var reusableSlice []ConfigData
    start = time.Now()
    for i := 0; i < iterations; i++ {
        reusableSlice = processConfigV3(configs, reusableSlice)
    }
    v3Time := time.Since(start)

    runtime.ReadMemStats(&m2)
    v3Allocs := m2.HeapObjects - m1.HeapObjects

    // Display results
    fmt.Printf("V1 (with escapes):    %v, %d allocations\n", v1Time, v1Allocs)
    fmt.Printf("V2 (fewer escapes):   %v, %d allocations\n", v2Time, v2Allocs)
    fmt.Printf("V3 (pre-allocated):   %v, %d allocations\n", v3Time, v3Allocs)

    fmt.Printf("\nPerformance improvements:\n")
    fmt.Printf("V2 vs V1: %.2fx faster, %d fewer allocations\n",
        float64(v1Time)/float64(v2Time), v1Allocs-v2Allocs)
    fmt.Printf("V3 vs V1: %.2fx faster, %d fewer allocations\n",
        float64(v1Time)/float64(v3Time), v1Allocs-v3Allocs)
}

// Demonstrate escape analysis command line tools
func demonstrateEscapeAnalysisTools() {
    fmt.Println("\n=== Escape Analysis Tools ===")

    fmt.Println("1. Build with escape analysis:")
    fmt.Println("   go build -gcflags=\"-m\" your_program.go")
    fmt.Println("   Shows which variables escape to heap")

    fmt.Println("\n2. More verbose escape analysis:")
    fmt.Println("   go build -gcflags=\"-m -m\" your_program.go")
    fmt.Println("   Shows detailed escape analysis decisions")

    fmt.Println("\n3. Disable optimizations for clearer analysis:")
    fmt.Println("   go build -gcflags=\"-m -N -l\" your_program.go")
    fmt.Println("   -N disables optimizations, -l disables inlining")

    fmt.Println("\n4. Memory profiling:")
    fmt.Println("   go build -o program your_program.go")
    fmt.Println("   ./program -memprofile=mem.prof")
    fmt.Println("   go tool pprof mem.prof")

    fmt.Println("\n5. Allocation tracing:")
    fmt.Println("   GODEBUG=allocfreetrace=1 ./program")
    fmt.Println("   Shows every allocation and free")

    fmt.Println("\n6. GC tracing:")
    fmt.Println("   GODEBUG=gctrace=1 ./program")
    fmt.Println("   Shows garbage collection statistics")
}

// Show common escape patterns and how to avoid them
func showEscapePatterns() {
    fmt.Println("\n=== Common Escape Patterns and Solutions ===")

    // Pattern 1: Returning pointer to local variable
    fmt.Println("1. Returning pointer to local variable:")
    fmt.Println("   BAD:  func bad() *int { x := 42; return &x }")
    fmt.Println("   GOOD: func good() int { return 42 }")

    // Pattern 2: Interface{} parameters
    fmt.Println("\n2. Interface{} parameters cause escapes:")
    fmt.Println("   BAD:  fmt.Printf(\"%v\", localVar) // localVar escapes")
    fmt.Println("   GOOD: Use specific types when possible")

    // Pattern 3: Slice of pointers
    fmt.Println("\n3. Slice of pointers:")
    fmt.Println("   BAD:  var ptrs []*MyStruct")
    fmt.Println("   GOOD: var values []MyStruct")

    // Pattern 4: Large values
    fmt.Println("\n4. Large values on stack:")
    fmt.Println("   BAD:  var huge [100000]int // May escape due to size")
    fmt.Println("   GOOD: Use smaller values or heap allocation explicitly")

    // Pattern 5: Closures
    fmt.Println("\n5. Closures capturing variables:")
    fmt.Println("   BAD:  func() { return func() { return localVar } }")
    fmt.Println("   GOOD: Pass values as parameters instead of capturing")
}

func main() {
    fmt.Println("=== Escape Analysis Tools and Optimization ===")
    fmt.Println("This program demonstrates escape analysis optimization techniques.")
    fmt.Println("Run with different flags to see escape analysis in action:")
    fmt.Println()

    benchmarkApproaches()
    demonstrateEscapeAnalysisTools()
    showEscapePatterns()

    fmt.Println("\n=== Memory Statistics ===")
    var m runtime.MemStats
    runtime.ReadMemStats(&m)
    fmt.Printf("Current heap objects: %d\n", m.HeapObjects)
    fmt.Printf("Current heap size: %d KB\n", m.HeapSys/1024)
    fmt.Printf("Total GC cycles: %d\n", m.NumGC)
}
```

**Key Takeaways**:

1. **Use escape analysis tools**: `go build -gcflags="-m"` is your friend
2. **Prefer value semantics** when possible to keep data on the stack
3. **Pre-allocate slices** and reuse them to reduce heap pressure
4. **Avoid unnecessary pointers** - not everything needs to be a pointer
5. **Profile before optimizing** - measure the actual impact
6. **Consider object pools** for frequently allocated/deallocated objects
7. **Be aware of interface{} costs** - they often cause escapes
8. **Design for the common case** - optimize hot paths, not edge cases

**Prerequisites**: Session 4

```go
// Memory-efficient data processing for automation
package main

import (
    "fmt"
    "runtime"
)

type LogEntry struct {
    Timestamp string
    Level     string
    Message   string
    Source    string
}

// Stack allocation - doesn't escape
func processLogEntryValue(entry LogEntry) LogEntry {
    entry.Level = "PROCESSED_" + entry.Level
    return entry
}

// Heap allocation - escapes to heap
func processLogEntryPointer(entry *LogEntry) *LogEntry {
    processed := &LogEntry{
        Timestamp: entry.Timestamp,
        Level:     "PROCESSED_" + entry.Level,
        Message:   entry.Message,
        Source:    entry.Source,
    }
    return processed
}

func main() {
    // Monitor memory usage
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    // Process many log entries
    for i := 0; i < 10000; i++ {
        entry := LogEntry{
            Timestamp: "2025-01-01T00:00:00Z",
            Level:     "INFO",
            Message:   fmt.Sprintf("Processing item %d", i),
            Source:    "automation-service",
        }

        // Use value semantics (stack allocation)
        _ = processLogEntryValue(entry)
    }

    runtime.GC()
    runtime.ReadMemStats(&m2)

    fmt.Printf("Memory allocated: %d bytes\n", m2.TotalAlloc-m1.TotalAlloc)
    fmt.Printf("Heap objects: %d\n", m2.HeapObjects)
}
```

**Prerequisites**: Session 4

---

### Session 6: Pointers Part 3: Stack Growth and Garbage Collection

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master Go's innovative stack growth mechanism and its advantages
- Understand garbage collection fundamentals and the tri-color algorithm
- Learn GC tuning techniques for automation workloads
- Monitor and profile memory usage effectively in Go applications
- Understand the relationship between allocation patterns and GC performance
- Apply memory optimization techniques for long-running automation systems

**Videos Covered**:

- 2.7 Pointers Part 4 (Stack Growth) (0:12:48)
- 2.8 Pointers Part 5 (Garbage Collection) (0:06:32)

**Key Concepts**:

**Stack Growth Mechanics**:

- Go uses segmented stacks that grow and shrink dynamically
- Initial stack size is small (2KB), can grow up to 1GB per goroutine
- Stack growth is handled automatically by the runtime
- Stack overflow is virtually impossible in Go
- Each goroutine has its own stack, enabling massive concurrency
- Stack growth involves copying the entire stack to a larger segment

**Garbage Collection Fundamentals**:

- Go uses a concurrent, tri-color mark-and-sweep collector
- Tri-color algorithm: white (unvisited), gray (visited, children not processed), black (fully processed)
- GC runs concurrently with your program to minimize stop-the-world pauses
- GC is triggered when heap size doubles since last collection (GOGC=100 default)
- Write barriers ensure correctness during concurrent collection

**GC Performance Characteristics**:

- GC latency is proportional to the number of pointers, not heap size
- Fewer pointers = faster GC cycles
- Value-heavy data structures are GC-friendly
- Pointer-heavy data structures increase GC overhead

**GC Tuning and Optimization**:

- GOGC environment variable controls GC frequency (default 100%)
- Lower GOGC = more frequent GC, less memory usage
- Higher GOGC = less frequent GC, more memory usage
- Monitor GC metrics to find optimal settings for your workload
- Consider allocation patterns when designing data structures

**Memory Profiling and Monitoring**:

- Use `go tool pprof` for detailed memory analysis
- GODEBUG=gctrace=1 for GC statistics
- runtime.ReadMemStats() for programmatic monitoring
- Understanding allocation sources and patterns
- Best practices for memory-efficient automation

**Hands-on Exercise**:

```go
// GC-aware batch processing system
package main

import (
    "fmt"
    "runtime"
    "runtime/debug"
    "time"
)

type BatchProcessor struct {
    batchSize int
    processed int
}

func (bp *BatchProcessor) ProcessBatch(items []string) {
    for _, item := range items {
        // Simulate processing work
        _ = fmt.Sprintf("Processing: %s", item)
        bp.processed++
    }

    // Force GC periodically for demonstration
    if bp.processed%1000 == 0 {
        runtime.GC()
        debug.FreeOSMemory()
    }
}

func main() {
    // Configure GC for automation workload
    debug.SetGCPercent(50) // More aggressive GC

    processor := &BatchProcessor{batchSize: 100}

    // Simulate processing large batches
    start := time.Now()

    for batch := 0; batch < 50; batch++ {
        items := make([]string, processor.batchSize)
        for i := range items {
            items[i] = fmt.Sprintf("item_%d_%d", batch, i)
        }

        processor.ProcessBatch(items)

        // Monitor memory every 10 batches
        if batch%10 == 0 {
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            fmt.Printf("Batch %d: Heap=%dKB, GC=%d\n",
                batch, m.HeapAlloc/1024, m.NumGC)
        }
    }

    duration := time.Since(start)
    fmt.Printf("Processed %d items in %v\n", processor.processed, duration)
}
```

**Prerequisites**: Session 5

---

### Session 7: Constants and Type Safety

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master Go's sophisticated constant system and its unique characteristics
- Understand the critical difference between typed and untyped constants
- Learn how constants provide implicit conversion capabilities
- Master iota for generating constant sequences and enumerations
- Understand constant expressions and compile-time evaluation
- Apply constants effectively for type safety in automation systems
- Learn best practices for configuration and enumeration constants

**Videos Covered**:

- 2.4 Constants (0:15:29)

**Key Concepts**:

**Constants vs Variables**:

- Constants exist only at compile time, not at runtime
- Constants have no memory address - they're replaced by their values
- Constants can be typed or untyped
- Constants enable implicit conversions that variables cannot
- Constant expressions are evaluated at compile time

**Untyped Constants**:

- Untyped constants have a "kind" but no specific type
- They can implicitly convert to compatible types
- Provide flexibility in numeric operations and assignments
- Examples: `const x = 42` (untyped integer), `const pi = 3.14159` (untyped float)

**Typed Constants**:

- Explicitly typed constants behave like variables for type checking
- No implicit conversion - explicit conversion required
- Examples: `const x int = 42`, `const pi float64 = 3.14159`

**Constant Arithmetic and Precision**:

- Constant arithmetic is performed with arbitrary precision
- Results are truncated/rounded only when assigned to variables
- Enables precise calculations at compile time
- Integer constants can be arbitrarily large during compilation

**iota - The Constant Generator**:

- iota is a predeclared identifier that generates sequential constants
- Resets to 0 at each `const` declaration
- Increments by 1 for each constant in the same declaration block
- Enables powerful enumeration and bit flag patterns
- Can be used in expressions for complex constant generation

**Constant Expressions**:

- Only certain operations are allowed in constant expressions
- Arithmetic, comparison, logical, and some built-in functions
- All operands must be constants
- Evaluated at compile time for maximum efficiency

**Hands-on Exercise 1: Typed vs Untyped Constants**:

```go
// Comprehensive demonstration of Go's constant system
package main

import (
    "fmt"
    "math"
)

func main() {
    demonstrateUntypedConstants()
    demonstrateTypedConstants()
    demonstrateConstantArithmetic()
    demonstrateImplicitConversion()
}

func demonstrateUntypedConstants() {
    fmt.Println("=== Untyped Constants ===")

    // Untyped constants - have kind but no specific type
    const untypedInt = 42
    const untypedFloat = 3.14159
    const untypedString = "Hello"
    const untypedBool = true

    // These can be assigned to compatible types
    var i8 int8 = untypedInt
    var i16 int16 = untypedInt
    var i32 int32 = untypedInt
    var i64 int64 = untypedInt

    var f32 float32 = untypedFloat
    var f64 float64 = untypedFloat

    fmt.Printf("Untyped int assigned to different types:\n")
    fmt.Printf("  int8: %d, int16: %d, int32: %d, int64: %d\n", i8, i16, i32, i64)
    fmt.Printf("Untyped float assigned to different types:\n")
    fmt.Printf("  float32: %f, float64: %f\n", f32, f64)

    // Untyped constants can participate in expressions with different types
    var result1 = untypedInt + i64  // untypedInt becomes int64
    var result2 = untypedFloat + f32 // untypedFloat becomes float32

    fmt.Printf("Mixed expressions: %d, %f\n", result1, result2)
    fmt.Println()
}

func demonstrateTypedConstants() {
    fmt.Println("=== Typed Constants ===")

    // Typed constants - have specific types
    const typedInt int = 42
    const typedFloat float64 = 3.14159
    const typedString string = "Hello"

    // These behave like variables for type checking
    var i64 int64
    var f32 float32

    // This would cause a compile error:
    // i64 = typedInt  // Cannot use typedInt (type int) as type int64

    // Explicit conversion required
    i64 = int64(typedInt)
    f32 = float32(typedFloat)

    fmt.Printf("Typed constants require explicit conversion:\n")
    fmt.Printf("  int64: %d, float32: %f\n", i64, f32)
    fmt.Println()
}

func demonstrateConstantArithmetic() {
    fmt.Println("=== Constant Arithmetic and Precision ===")

    // Constants have arbitrary precision during compilation
    const huge = 1000000000000000000000000000000000000000000
    const precise = 1.23456789012345678901234567890123456789

    // Arithmetic is performed with full precision
    const calculated = huge * 2
    const division = precise / 3

    fmt.Printf("Huge constant: %e\n", float64(huge))
    fmt.Printf("Precise constant: %.30f\n", precise)
    fmt.Printf("Calculated: %e\n", float64(calculated))
    fmt.Printf("Division result: %.30f\n", division)

    // Demonstrate compile-time evaluation
    const compiletime = 2 * 3 * 4 * 5 * 6 * 7 * 8 * 9 * 10
    fmt.Printf("Compile-time calculation: %d\n", compiletime)

    // Complex constant expressions
    const complex = (1 << 10) + (1 << 20) + (1 << 30)
    fmt.Printf("Complex expression: %d\n", complex)
    fmt.Println()
}

func demonstrateImplicitConversion() {
    fmt.Println("=== Implicit Conversion with Constants ===")

    const factor = 2.5

    // Untyped constant can be used with different numeric types
    var i int = 10
    var f float64 = 20.0

    // These work because factor is untyped
    result1 := float64(i) * factor  // factor becomes float64
    result2 := f * factor           // factor becomes float64

    fmt.Printf("Implicit conversion results: %f, %f\n", result1, result2)

    // Demonstrate with function calls
    processInt(42)      // untyped constant
    processFloat(42)    // same untyped constant, different type
    processFloat(42.0)  // untyped float constant

    fmt.Println()
}

func processInt(x int) {
    fmt.Printf("Processing int: %d\n", x)
}

func processFloat(x float64) {
    fmt.Printf("Processing float: %f\n", x)
}
```

**Hands-on Exercise 2: iota and Enumeration Patterns**:

```go
// Advanced iota patterns for automation systems
package main

import "fmt"

// Basic enumeration using iota
type TaskStatus int

const (
    TaskPending TaskStatus = iota  // 0
    TaskRunning                    // 1
    TaskCompleted                  // 2
    TaskFailed                     // 3
    TaskCancelled                  // 4
)

func (ts TaskStatus) String() string {
    switch ts {
    case TaskPending:
        return "Pending"
    case TaskRunning:
        return "Running"
    case TaskCompleted:
        return "Completed"
    case TaskFailed:
        return "Failed"
    case TaskCancelled:
        return "Cancelled"
    default:
        return "Unknown"
    }
}

// Skip values with iota
type Priority int

const (
    Low Priority = iota * 10  // 0
    Medium                    // 10
    High                      // 20
    Critical                  // 30
)

// Bit flags using iota
type Permission int

const (
    Read Permission = 1 << iota  // 1 (binary: 001)
    Write                        // 2 (binary: 010)
    Execute                      // 4 (binary: 100)
)

// Complex iota expressions
type Size int

const (
    _        = iota             // ignore first value
    KB Size  = 1 << (10 * iota) // 1024
    MB                          // 1048576
    GB                          // 1073741824
    TB                          // 1099511627776
)

// Multiple constant blocks reset iota
const (
    First = iota   // 0
    Second         // 1
)

const (
    Third = iota   // 0 (reset)
    Fourth         // 1
)

// Configuration constants for automation
const (
    DefaultTimeout     = 30  // seconds
    MaxRetries        = 3
    DefaultBufferSize = 1024
    MaxWorkers        = 100
)

// String constants for configuration
const (
    ConfigFile     = "automation.yaml"
    LogFile        = "automation.log"
    DefaultLogLevel = "INFO"
)

func main() {
    demonstrateBasicIota()
    demonstrateAdvancedIota()
    demonstrateBitFlags()
    demonstrateConfigConstants()
}

func demonstrateBasicIota() {
    fmt.Println("=== Basic iota Enumeration ===")

    statuses := []TaskStatus{TaskPending, TaskRunning, TaskCompleted, TaskFailed, TaskCancelled}

    for _, status := range statuses {
        fmt.Printf("Status %d: %s\n", int(status), status.String())
    }

    priorities := []Priority{Low, Medium, High, Critical}
    for _, priority := range priorities {
        fmt.Printf("Priority value: %d\n", int(priority))
    }

    fmt.Println()
}

func demonstrateAdvancedIota() {
    fmt.Println("=== Advanced iota Patterns ===")

    sizes := []Size{KB, MB, GB, TB}
    sizeNames := []string{"KB", "MB", "GB", "TB"}

    for i, size := range sizes {
        fmt.Printf("1 %s = %d bytes\n", sizeNames[i], int(size))
    }

    fmt.Printf("First: %d, Second: %d\n", First, Second)
    fmt.Printf("Third: %d, Fourth: %d\n", Third, Fourth)

    fmt.Println()
}

func demonstrateBitFlags() {
    fmt.Println("=== Bit Flags with iota ===")

    fmt.Printf("Read: %d (binary: %08b)\n", Read, Read)
    fmt.Printf("Write: %d (binary: %08b)\n", Write, Write)
    fmt.Printf("Execute: %d (binary: %08b)\n", Execute, Execute)

    // Combine permissions
    readWrite := Read | Write
    fullAccess := Read | Write | Execute

    fmt.Printf("Read+Write: %d (binary: %08b)\n", readWrite, readWrite)
    fmt.Printf("Full access: %d (binary: %08b)\n", fullAccess, fullAccess)

    // Check permissions
    fmt.Printf("Has read permission: %t\n", hasPermission(fullAccess, Read))
    fmt.Printf("Has write permission: %t\n", hasPermission(readWrite, Write))
    fmt.Printf("Has execute permission: %t\n", hasPermission(readWrite, Execute))

    fmt.Println()
}

func hasPermission(permissions, check Permission) bool {
    return permissions&check != 0
}

func demonstrateConfigConstants() {
    fmt.Println("=== Configuration Constants ===")

    fmt.Printf("Default timeout: %d seconds\n", DefaultTimeout)
    fmt.Printf("Max retries: %d\n", MaxRetries)
    fmt.Printf("Buffer size: %d bytes\n", DefaultBufferSize)
    fmt.Printf("Max workers: %d\n", MaxWorkers)

    fmt.Printf("Config file: %s\n", ConfigFile)
    fmt.Printf("Log file: %s\n", LogFile)
    fmt.Printf("Log level: %s\n", DefaultLogLevel)

    // Constants in expressions
    totalBufferSize := DefaultBufferSize * MaxWorkers
    fmt.Printf("Total buffer size: %d bytes\n", totalBufferSize)
}
```

**Hands-on Exercise 3: Automation System Configuration with Constants**:

```go
// Real-world automation system using constants effectively
package main

import (
    "fmt"
    "time"
)

// System-wide configuration constants
const (
    // Application metadata
    AppName    = "AutomationEngine"
    AppVersion = "2.1.0"

    // Timeouts and intervals (using time.Duration for type safety)
    DefaultTimeout    = 30 * time.Second
    HeartbeatInterval = 5 * time.Second
    RetryDelay        = 2 * time.Second

    // Limits and thresholds
    MaxConcurrentTasks = 50
    MaxRetryAttempts   = 3
    MaxLogFileSize     = 100 * 1024 * 1024 // 100MB

    // Network configuration
    DefaultPort     = 8080
    MaxConnections  = 1000
    ReadBufferSize  = 4096
    WriteBufferSize = 4096
)

// Environment types
type Environment int

const (
    Development Environment = iota
    Testing
    Staging
    Production
)

func (e Environment) String() string {
    switch e {
    case Development:
        return "development"
    case Testing:
        return "testing"
    case Staging:
        return "staging"
    case Production:
        return "production"
    default:
        return "unknown"
    }
}

// Log levels with explicit values
type LogLevel int

const (
    LogTrace LogLevel = iota
    LogDebug
    LogInfo
    LogWarn
    LogError
    LogFatal
)

func (ll LogLevel) String() string {
    levels := []string{"TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"}
    if int(ll) < len(levels) {
        return levels[ll]
    }
    return "UNKNOWN"
}

// Task types using string constants for clarity
const (
    TaskTypeDataSync     = "data_sync"
    TaskTypeFileProcess  = "file_process"
    TaskTypeNotification = "notification"
    TaskTypeCleanup      = "cleanup"
    TaskTypeBackup       = "backup"
)

// Error codes using iota with gaps for future expansion
type ErrorCode int

const (
    ErrSuccess ErrorCode = 0

    // Client errors (1000-1999)
    ErrInvalidInput ErrorCode = 1000 + iota
    ErrMissingParameter
    ErrInvalidFormat
    ErrAuthenticationFailed

    // Server errors (2000-2999)
    ErrInternalServer ErrorCode = 2000 + iota
    ErrDatabaseConnection
    ErrServiceUnavailable
    ErrTimeout

    // System errors (3000-3999)
    ErrSystemOverload ErrorCode = 3000 + iota
    ErrDiskFull
    ErrMemoryExhausted
    ErrNetworkFailure
)

func (ec ErrorCode) String() string {
    switch {
    case ec == ErrSuccess:
        return "Success"
    case ec >= 1000 && ec < 2000:
        return "Client Error"
    case ec >= 2000 && ec < 3000:
        return "Server Error"
    case ec >= 3000 && ec < 4000:
        return "System Error"
    default:
        return "Unknown Error"
    }
}

// Configuration structure using constants
type Config struct {
    Environment     Environment
    LogLevel        LogLevel
    Port            int
    MaxConnections  int
    Timeout         time.Duration
    RetryAttempts   int
    BufferSize      int
}

// Factory function using constants for defaults
func NewConfig(env Environment) Config {
    config := Config{
        Environment:    env,
        Port:          DefaultPort,
        MaxConnections: MaxConnections,
        Timeout:       DefaultTimeout,
        RetryAttempts: MaxRetryAttempts,
        BufferSize:    ReadBufferSize,
    }

    // Environment-specific overrides
    switch env {
    case Development:
        config.LogLevel = LogDebug
        config.MaxConnections = 10
    case Testing:
        config.LogLevel = LogInfo
        config.MaxConnections = 50
        config.Timeout = 10 * time.Second
    case Staging:
        config.LogLevel = LogInfo
        config.MaxConnections = 500
    case Production:
        config.LogLevel = LogWarn
        config.Timeout = 60 * time.Second
    }

    return config
}

// Task structure using constant types
type Task struct {
    ID       string
    Type     string
    Status   TaskStatus
    Priority Priority
    Created  time.Time
    Config   Config
}

func main() {
    demonstrateConfigurationSystem()
    demonstrateErrorHandling()
    demonstrateTaskManagement()
}

func demonstrateConfigurationSystem() {
    fmt.Println("=== Configuration System with Constants ===")

    environments := []Environment{Development, Testing, Staging, Production}

    for _, env := range environments {
        config := NewConfig(env)
        fmt.Printf("\n%s Configuration:\n", env.String())
        fmt.Printf("  Log Level: %s\n", config.LogLevel.String())
        fmt.Printf("  Port: %d\n", config.Port)
        fmt.Printf("  Max Connections: %d\n", config.MaxConnections)
        fmt.Printf("  Timeout: %v\n", config.Timeout)
        fmt.Printf("  Retry Attempts: %d\n", config.RetryAttempts)
        fmt.Printf("  Buffer Size: %d bytes\n", config.BufferSize)
    }

    fmt.Printf("\nApplication: %s v%s\n", AppName, AppVersion)
    fmt.Printf("Heartbeat Interval: %v\n", HeartbeatInterval)
    fmt.Printf("Max Log File Size: %d MB\n", MaxLogFileSize/(1024*1024))
}

func demonstrateErrorHandling() {
    fmt.Println("\n=== Error Handling with Constants ===")

    errors := []ErrorCode{
        ErrSuccess,
        ErrInvalidInput,
        ErrMissingParameter,
        ErrInternalServer,
        ErrDatabaseConnection,
        ErrSystemOverload,
        ErrDiskFull,
    }

    for _, err := range errors {
        fmt.Printf("Error %d: %s (Category: %s)\n",
            int(err), getErrorMessage(err), err.String())
    }
}

func getErrorMessage(code ErrorCode) string {
    switch code {
    case ErrSuccess:
        return "Operation completed successfully"
    case ErrInvalidInput:
        return "Invalid input provided"
    case ErrMissingParameter:
        return "Required parameter missing"
    case ErrInternalServer:
        return "Internal server error"
    case ErrDatabaseConnection:
        return "Database connection failed"
    case ErrSystemOverload:
        return "System is overloaded"
    case ErrDiskFull:
        return "Disk space exhausted"
    default:
        return "Unknown error occurred"
    }
}

func demonstrateTaskManagement() {
    fmt.Println("\n=== Task Management with Constants ===")

    config := NewConfig(Production)

    tasks := []Task{
        {
            ID:       "task-001",
            Type:     TaskTypeDataSync,
            Status:   TaskRunning,
            Priority: High,
            Created:  time.Now(),
            Config:   config,
        },
        {
            ID:       "task-002",
            Type:     TaskTypeFileProcess,
            Status:   TaskPending,
            Priority: Medium,
            Created:  time.Now(),
            Config:   config,
        },
        {
            ID:       "task-003",
            Type:     TaskTypeBackup,
            Status:   TaskCompleted,
            Priority: Low,
            Created:  time.Now().Add(-time.Hour),
            Config:   config,
        },
    }

    for _, task := range tasks {
        fmt.Printf("\nTask %s:\n", task.ID)
        fmt.Printf("  Type: %s\n", task.Type)
        fmt.Printf("  Status: %s\n", task.Status.String())
        fmt.Printf("  Priority: %d\n", int(task.Priority))
        fmt.Printf("  Created: %s\n", task.Created.Format(time.RFC3339))
        fmt.Printf("  Environment: %s\n", task.Config.Environment.String())
    }

    // Demonstrate constant-based logic
    fmt.Println("\n=== Task Processing Logic ===")
    for _, task := range tasks {
        processTask(task)
    }
}

func processTask(task Task) {
    fmt.Printf("Processing task %s (%s)...\n", task.ID, task.Type)

    // Use constants for decision making
    switch task.Type {
    case TaskTypeDataSync:
        fmt.Printf("  Syncing data with timeout %v\n", task.Config.Timeout)
    case TaskTypeFileProcess:
        fmt.Printf("  Processing files with buffer size %d\n", task.Config.BufferSize)
    case TaskTypeBackup:
        fmt.Printf("  Creating backup with %d retry attempts\n", task.Config.RetryAttempts)
    case TaskTypeNotification:
        fmt.Printf("  Sending notifications\n")
    case TaskTypeCleanup:
        fmt.Printf("  Cleaning up temporary files\n")
    default:
        fmt.Printf("  Unknown task type: %s\n", task.Type)
    }
}
```

**Key Benefits of Using Constants**:

1. **Type Safety**: Typed constants prevent accidental misuse
2. **Compile-time Optimization**: Constants are replaced by values at compile time
3. **Code Clarity**: Named constants make code self-documenting
4. **Maintainability**: Centralized configuration values
5. **Performance**: No runtime overhead for constant values
6. **Flexibility**: Untyped constants provide implicit conversion capabilities

**Best Practices**:

1. Use untyped constants for flexibility when possible
2. Group related constants together
3. Use iota for sequential values and enumerations
4. Prefer typed constants for domain-specific values
5. Use descriptive names that indicate purpose and units
6. Consider using custom types for better type safety
7. Document complex constant expressions

**Prerequisites**: Session 6

```go
// Configuration constants for automation system
package main

import "fmt"

// Service status constants using iota
type ServiceStatus int

const (
    StatusUnknown ServiceStatus = iota
    StatusStarting
    StatusRunning
    StatusStopping
    StatusStopped
    StatusError
)

// Configuration constants
const (
    DefaultTimeout = 30 // untyped constant
    MaxRetries     = 3
    BufferSize     = 1024
)

// String representation for status
func (s ServiceStatus) String() string {
    switch s {
    case StatusUnknown:
        return "Unknown"
    case StatusStarting:
        return "Starting"
    case StatusRunning:
        return "Running"
    case StatusStopping:
        return "Stopping"
    case StatusStopped:
        return "Stopped"
    case StatusError:
        return "Error"
    default:
        return "Invalid"
    }
}

type AutomationService struct {
    Name   string
    Status ServiceStatus
}

func main() {
    services := []AutomationService{
        {"Database Monitor", StatusRunning},
        {"Log Processor", StatusStarting},
        {"Alert Manager", StatusError},
    }

    fmt.Println("Service Status Report:")
    for _, service := range services {
        fmt.Printf("- %s: %s (%d)\n", service.Name, service.Status, service.Status)
    }

    // Demonstrate constant usage
    fmt.Printf("\nConfiguration:\n")
    fmt.Printf("- Default Timeout: %d seconds\n", DefaultTimeout)
    fmt.Printf("- Max Retries: %d\n", MaxRetries)
    fmt.Printf("- Buffer Size: %d bytes\n", BufferSize)
}
```

**Prerequisites**: Session 6

---

### Session 8: Data-Oriented Design Principles

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master data-oriented design philosophy and its advantages over object-oriented design
- Understand mechanical sympathy and hardware-aware programming
- Learn to optimize data structures for CPU cache efficiency
- Apply data transformation principles for performance
- Design efficient data layouts for automation workloads
- Understand the relationship between data layout and algorithm performance

**Videos Covered**:

- 3.1 Topics (0:00:41)
- 3.2 Data-Oriented Design (0:04:52)

**Key Concepts**:

**Data-Oriented Design Philosophy**:

- Data drives everything - if you don't understand the data, you don't understand the problem
- Focus on data transformations rather than object behaviors
- Optimize for the common case, not edge cases
- Design data structures based on how they will be accessed and processed
- Separate data from behavior for better performance and maintainability

**Mechanical Sympathy**:

- Understanding how hardware works to write efficient software
- CPU cache hierarchy: L1, L2, L3 caches and main memory
- Cache line size (typically 64 bytes) and its impact on performance
- Prefetching and spatial locality principles
- Memory access patterns that work with hardware, not against it

**Cache-Friendly Data Structures**:

- Struct of Arrays (SoA) vs Array of Structs (AoS)
- Data layout optimization for sequential access
- Minimizing cache misses through better data organization
- Hot/cold data separation
- Padding and alignment considerations

**Data Transformation Approach**:

- Think in terms of data pipelines and transformations
- Batch processing for better cache utilization
- Minimize data movement and copying
- Use appropriate data structures for access patterns
- Mechanical sympathy principles
- Performance implications of data organization

**Hands-on Exercise**:

```go
// Efficient data structures for log processing
package main

import (
    "fmt"
    "time"
    "unsafe"
)

// Poor design - scattered data
type LogEntryPoor struct {
    ID        int64
    Timestamp time.Time
    Level     string
    Message   string
    Source    string
    Tags      map[string]string
}

// Better design - grouped by access patterns
type LogBatch struct {
    // Hot data - frequently accessed together
    IDs        []int64
    Timestamps []int64 // Unix timestamps for better cache locality
    Levels     []uint8 // Encoded levels (0=DEBUG, 1=INFO, etc.)

    // Cold data - less frequently accessed
    Messages []string
    Sources  []string
}

// Level encoding for better memory efficiency
const (
    LevelDebug uint8 = iota
    LevelInfo
    LevelWarn
    LevelError
    LevelFatal
)

func (lb *LogBatch) AddEntry(id int64, timestamp time.Time, level uint8, message, source string) {
    lb.IDs = append(lb.IDs, id)
    lb.Timestamps = append(lb.Timestamps, timestamp.Unix())
    lb.Levels = append(lb.Levels, level)
    lb.Messages = append(lb.Messages, message)
    lb.Sources = append(lb.Sources, source)
}

func (lb *LogBatch) CountByLevel(targetLevel uint8) int {
    count := 0
    // Cache-friendly iteration - only touching level data
    for _, level := range lb.Levels {
        if level == targetLevel {
            count++
        }
    }
    return count
}

func main() {
    // Compare memory usage
    fmt.Printf("LogEntryPoor size: %d bytes\n", unsafe.Sizeof(LogEntryPoor{}))

    // Create efficient log batch
    batch := &LogBatch{}

    // Add sample entries
    now := time.Now()
    for i := 0; i < 1000; i++ {
        batch.AddEntry(
            int64(i),
            now.Add(time.Duration(i)*time.Second),
            LevelInfo,
            fmt.Sprintf("Processing item %d", i),
            "automation-service",
        )
    }

    // Efficient processing - only touches needed data
    errorCount := batch.CountByLevel(LevelError)
    infoCount := batch.CountByLevel(LevelInfo)

    fmt.Printf("Processed %d entries\n", len(batch.IDs))
    fmt.Printf("Info entries: %d, Error entries: %d\n", infoCount, errorCount)
}
```

**Prerequisites**: Session 7

---

### Session 9: Arrays: Mechanical Sympathy and Performance

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand array fundamentals and memory layout
- Learn mechanical sympathy principles with arrays
- Master array performance characteristics
- Apply arrays effectively in automation scenarios

**Videos Covered**:

- 3.3 Arrays Part 1 (Mechanical Sympathy) (0:33:10)

**Key Concepts**:

- Array declaration and initialization
- Contiguous memory layout and cache performance
- Array vs slice performance trade-offs
- Fixed-size collections in automation
- Memory predictability and allocation

**Hands-on Exercise 1: High-Performance Metrics Collection**:

```go
// High-performance metrics collection using arrays
package main

import (
    "fmt"
    "time"
)

const (
    MetricsBufferSize = 1000
    SampleCount      = 100
)

// Fixed-size metrics buffer for predictable performance
type MetricsCollector struct {
    cpuUsage    [MetricsBufferSize]float64
    memoryUsage [MetricsBufferSize]float64
    timestamps  [MetricsBufferSize]int64
    index       int
    full        bool
}

func (mc *MetricsCollector) AddMetric(cpu, memory float64) {
    mc.cpuUsage[mc.index] = cpu
    mc.memoryUsage[mc.index] = memory
    mc.timestamps[mc.index] = time.Now().Unix()

    mc.index++
    if mc.index >= MetricsBufferSize {
        mc.index = 0
        mc.full = true
    }
}

func (mc *MetricsCollector) GetAverages() (float64, float64) {
    count := mc.index
    if mc.full {
        count = MetricsBufferSize
    }

    if count == 0 {
        return 0, 0
    }

    var cpuSum, memSum float64
    for i := 0; i < count; i++ {
        cpuSum += mc.cpuUsage[i]
        memSum += mc.memoryUsage[i]
    }

    return cpuSum / float64(count), memSum / float64(count)
}

func main() {
    collector := &MetricsCollector{}

    // Simulate collecting metrics
    fmt.Println("Collecting system metrics...")
    for i := 0; i < SampleCount; i++ {
        // Simulate CPU and memory readings
        cpu := 20.0 + float64(i%50)
        memory := 60.0 + float64(i%30)

        collector.AddMetric(cpu, memory)

        if i%20 == 0 {
            avgCPU, avgMem := collector.GetAverages()
            fmt.Printf("Sample %d - Avg CPU: %.2f%%, Avg Memory: %.2f%%\n",
                i, avgCPU, avgMem)
        }
    }

    finalCPU, finalMem := collector.GetAverages()
    fmt.Printf("\nFinal averages - CPU: %.2f%%, Memory: %.2f%%\n",
        finalCPU, finalMem)
}
```

**Hands-on Exercise 2: Cache Performance Analysis**:

```go
// Demonstrate mechanical sympathy with array traversal patterns
package main

import (
    "fmt"
    "time"
)

const (
    MatrixSize = 2000
    Iterations = 5
)

// Large matrix for cache performance testing
type Matrix [MatrixSize][MatrixSize]int

func main() {
    matrix := &Matrix{}

    // Initialize matrix with test data
    initializeMatrix(matrix)

    // Test different traversal patterns
    fmt.Println("=== Cache Performance Analysis ===")

    // Row-major traversal (cache-friendly)
    rowTime := benchmarkRowTraversal(matrix)
    fmt.Printf("Row-major traversal: %v\n", rowTime)

    // Column-major traversal (cache-unfriendly)
    colTime := benchmarkColumnTraversal(matrix)
    fmt.Printf("Column-major traversal: %v\n", colTime)

    // Performance difference
    ratio := float64(colTime) / float64(rowTime)
    fmt.Printf("Column/Row ratio: %.2fx slower\n", ratio)

    // Block traversal (cache-optimized)
    blockTime := benchmarkBlockTraversal(matrix)
    fmt.Printf("Block traversal: %v\n", blockTime)

    blockRatio := float64(blockTime) / float64(rowTime)
    fmt.Printf("Block/Row ratio: %.2fx\n", blockRatio)
}

func initializeMatrix(matrix *Matrix) {
    for i := 0; i < MatrixSize; i++ {
        for j := 0; j < MatrixSize; j++ {
            matrix[i][j] = i*MatrixSize + j
        }
    }
}

func benchmarkRowTraversal(matrix *Matrix) time.Duration {
    start := time.Now()

    for iter := 0; iter < Iterations; iter++ {
        sum := 0
        for i := 0; i < MatrixSize; i++ {
            for j := 0; j < MatrixSize; j++ {
                sum += matrix[i][j]
            }
        }
        _ = sum // Prevent optimization
    }

    return time.Since(start)
}

func benchmarkColumnTraversal(matrix *Matrix) time.Duration {
    start := time.Now()

    for iter := 0; iter < Iterations; iter++ {
        sum := 0
        for j := 0; j < MatrixSize; j++ {
            for i := 0; i < MatrixSize; i++ {
                sum += matrix[i][j]
            }
        }
        _ = sum // Prevent optimization
    }

    return time.Since(start)
}

func benchmarkBlockTraversal(matrix *Matrix) time.Duration {
    const BlockSize = 64
    start := time.Now()

    for iter := 0; iter < Iterations; iter++ {
        sum := 0
        for bi := 0; bi < MatrixSize; bi += BlockSize {
            for bj := 0; bj < MatrixSize; bj += BlockSize {
                // Process block
                maxI := min(bi+BlockSize, MatrixSize)
                maxJ := min(bj+BlockSize, MatrixSize)
                for i := bi; i < maxI; i++ {
                    for j := bj; j < maxJ; j++ {
                        sum += matrix[i][j]
                    }
                }
            }
        }
        _ = sum // Prevent optimization
    }

    return time.Since(start)
}

func min(a, b int) int {
    if a < b {
        return a
    }
    return b
}
```

**Hands-on Exercise 3: Fixed-Size Task Queue**:

```go
// Fixed-size task queue for predictable memory usage
package main

import (
    "fmt"
    "time"
)

const (
    QueueSize = 100
    TaskTypes = 5
)

// Task represents an automation task
type Task struct {
    ID       int
    Type     int
    Priority int
    Data     string
}

// Fixed-size circular queue using arrays
type TaskQueue struct {
    tasks [QueueSize]Task
    head  int
    tail  int
    count int
    full  bool
}

func NewTaskQueue() *TaskQueue {
    return &TaskQueue{}
}

func (tq *TaskQueue) Enqueue(task Task) bool {
    if tq.full {
        return false // Queue is full
    }

    tq.tasks[tq.tail] = task
    tq.tail = (tq.tail + 1) % QueueSize
    tq.count++

    if tq.tail == tq.head {
        tq.full = true
    }

    return true
}

func (tq *TaskQueue) Dequeue() (Task, bool) {
    if tq.count == 0 {
        return Task{}, false // Queue is empty
    }

    task := tq.tasks[tq.head]
    tq.head = (tq.head + 1) % QueueSize
    tq.count--
    tq.full = false

    return task, true
}

func (tq *TaskQueue) Size() int {
    return tq.count
}

func (tq *TaskQueue) IsFull() bool {
    return tq.full
}

func (tq *TaskQueue) IsEmpty() bool {
    return tq.count == 0
}

// Get statistics by task type
func (tq *TaskQueue) GetTypeStats() [TaskTypes]int {
    var stats [TaskTypes]int

    if tq.count == 0 {
        return stats
    }

    // Traverse the circular queue
    for i := 0; i < tq.count; i++ {
        index := (tq.head + i) % QueueSize
        taskType := tq.tasks[index].Type
        if taskType >= 0 && taskType < TaskTypes {
            stats[taskType]++
        }
    }

    return stats
}

func main() {
    queue := NewTaskQueue()

    fmt.Println("=== Fixed-Size Task Queue Demo ===")

    // Add tasks to queue
    tasks := []Task{
        {ID: 1, Type: 0, Priority: 5, Data: "Process file A"},
        {ID: 2, Type: 1, Priority: 3, Data: "Send notification"},
        {ID: 3, Type: 0, Priority: 7, Data: "Process file B"},
        {ID: 4, Type: 2, Priority: 1, Data: "Cleanup temp files"},
        {ID: 5, Type: 1, Priority: 4, Data: "Send alert"},
        {ID: 6, Type: 3, Priority: 6, Data: "Backup database"},
        {ID: 7, Type: 0, Priority: 2, Data: "Process file C"},
    }

    // Enqueue tasks
    for _, task := range tasks {
        if queue.Enqueue(task) {
            fmt.Printf("Enqueued task %d (type %d)\n", task.ID, task.Type)
        } else {
            fmt.Printf("Failed to enqueue task %d - queue full\n", task.ID)
        }
    }

    fmt.Printf("\nQueue size: %d\n", queue.Size())

    // Show type statistics
    stats := queue.GetTypeStats()
    fmt.Println("\nTask type statistics:")
    typeNames := []string{"File Processing", "Notifications", "Cleanup", "Backup", "Other"}
    for i, count := range stats {
        if count > 0 {
            fmt.Printf("  %s: %d tasks\n", typeNames[i], count)
        }
    }

    // Process some tasks
    fmt.Println("\nProcessing tasks:")
    for i := 0; i < 3; i++ {
        if task, ok := queue.Dequeue(); ok {
            fmt.Printf("Processing task %d: %s\n", task.ID, task.Data)
            time.Sleep(100 * time.Millisecond) // Simulate processing
        }
    }

    fmt.Printf("\nRemaining queue size: %d\n", queue.Size())

    // Add more tasks
    newTasks := []Task{
        {ID: 8, Type: 4, Priority: 8, Data: "Emergency task"},
        {ID: 9, Type: 2, Priority: 1, Data: "Regular cleanup"},
    }

    for _, task := range newTasks {
        if queue.Enqueue(task) {
            fmt.Printf("Added new task %d\n", task.ID)
        }
    }

    fmt.Printf("Final queue size: %d\n", queue.Size())
}
```

**Prerequisites**: Session 8

---

### Session 10: Arrays: Semantics and Value Types

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master array value semantics and copying behavior
- Understand when arrays are appropriate vs slices
- Learn array comparison and assignment rules
- Apply array semantics in automation contexts

**Videos Covered**:

- 3.4 Arrays Part 2 (Semantics) (0:16:43)

**Key Concepts**:

- Arrays are value types (copied on assignment)
- Array comparison and equality
- Passing arrays to functions (copy vs pointer)
- Array literals and initialization patterns
- When to choose arrays over slices

**Hands-on Exercise 1: Configuration Validation with Array Semantics**:

```go
// Configuration validation using array semantics
package main

import "fmt"

const MaxConfigItems = 10

type ConfigValidator struct {
    requiredFields [MaxConfigItems]string
    fieldCount     int
}

func NewConfigValidator(fields []string) ConfigValidator {
    var validator ConfigValidator

    // Copy fields into fixed array
    for i, field := range fields {
        if i >= MaxConfigItems {
            break
        }
        validator.requiredFields[i] = field
        validator.fieldCount++
    }

    return validator // Array is copied by value
}

func (cv ConfigValidator) Validate(config map[string]string) []string {
    var missing []string

    // Array iteration - working with copy
    for i := 0; i < cv.fieldCount; i++ {
        field := cv.requiredFields[i]
        if _, exists := config[field]; !exists {
            missing = append(missing, field)
        }
    }

    return missing
}

// Demonstrate array comparison
func (cv ConfigValidator) Equals(other ConfigValidator) bool {
    return cv.requiredFields == other.requiredFields &&
           cv.fieldCount == other.fieldCount
}

func main() {
    // Create validators with different required fields
    webValidator := NewConfigValidator([]string{
        "server_port", "database_url", "api_key", "log_level",
    })

    dbValidator := NewConfigValidator([]string{
        "host", "port", "username", "password", "database",
    })

    // Test configurations
    webConfig := map[string]string{
        "server_port":  "8080",
        "database_url": "localhost:5432",
        "api_key":      "secret123",
        // missing log_level
    }

    dbConfig := map[string]string{
        "host":     "localhost",
        "port":     "5432",
        "username": "admin",
        "password": "secret",
        "database": "myapp",
    }

    // Validate configurations
    if missing := webValidator.Validate(webConfig); len(missing) > 0 {
        fmt.Printf("Web config missing: %v\n", missing)
    } else {
        fmt.Println("Web config is valid")
    }

    if missing := dbValidator.Validate(dbConfig); len(missing) > 0 {
        fmt.Printf("DB config missing: %v\n", missing)
    } else {
        fmt.Println("DB config is valid")
    }

    // Demonstrate array comparison
    webValidator2 := NewConfigValidator([]string{
        "server_port", "database_url", "api_key", "log_level",
    })

    fmt.Printf("Validators equal: %t\n", webValidator.Equals(webValidator2))
}
```

**Hands-on Exercise 2: Array Value Semantics vs Pointer Semantics**:

```go
// Demonstrate array copying behavior and when to use pointers
package main

import (
    "fmt"
    "unsafe"
)

const (
    BufferSize = 1000
    DataPoints = 100
)

// Large array for demonstration
type DataBuffer [BufferSize]float64

// Processor that works with array values (copies)
type ValueProcessor struct {
    buffer DataBuffer
    count  int
}

// Processor that works with array pointers (shared)
type PointerProcessor struct {
    buffer *DataBuffer
    count  int
}

func NewValueProcessor() ValueProcessor {
    return ValueProcessor{}
}

func NewPointerProcessor() PointerProcessor {
    return PointerProcessor{
        buffer: &DataBuffer{},
    }
}

// Value receiver - operates on copy
func (vp ValueProcessor) ProcessData(data DataBuffer) DataBuffer {
    fmt.Printf("Value processor - buffer address: %p\n", &vp.buffer)

    // Modify the copy
    for i := 0; i < BufferSize && i < len(data); i++ {
        vp.buffer[i] = data[i] * 2.0
    }

    return vp.buffer // Returns a copy
}

// Pointer receiver - operates on shared data
func (pp *PointerProcessor) ProcessData(data *DataBuffer) {
    fmt.Printf("Pointer processor - buffer address: %p\n", pp.buffer)

    // Modify the original
    for i := 0; i < BufferSize; i++ {
        pp.buffer[i] = data[i] * 2.0
    }
}

func main() {
    fmt.Println("=== Array Value vs Pointer Semantics ===")

    // Create test data
    var originalData DataBuffer
    for i := 0; i < DataPoints; i++ {
        originalData[i] = float64(i + 1)
    }

    fmt.Printf("Original data address: %p\n", &originalData)
    fmt.Printf("Original data size: %d bytes\n", unsafe.Sizeof(originalData))

    // Test value semantics
    fmt.Println("\n--- Value Semantics Test ---")
    valueProc := NewValueProcessor()

    // This creates a copy of the entire array
    processedData := valueProc.ProcessData(originalData)
    fmt.Printf("Processed data address: %p\n", &processedData)

    // Original data unchanged
    fmt.Printf("Original[0]: %.1f, Processed[0]: %.1f\n",
        originalData[0], processedData[0])

    // Test pointer semantics
    fmt.Println("\n--- Pointer Semantics Test ---")
    pointerProc := NewPointerProcessor()

    // Create a copy for pointer processing
    var dataForPointer DataBuffer = originalData
    fmt.Printf("Data for pointer address: %p\n", &dataForPointer)

    // This works with the original data (no copy)
    pointerProc.ProcessData(&dataForPointer)

    // Original data is modified
    fmt.Printf("After pointer processing[0]: %.1f\n", dataForPointer[0])

    // Demonstrate array assignment (copy)
    fmt.Println("\n--- Array Assignment Test ---")
    var array1 DataBuffer
    var array2 DataBuffer

    // Initialize array1
    for i := 0; i < 10; i++ {
        array1[i] = float64(i * 10)
    }

    fmt.Printf("Array1 address: %p\n", &array1)
    fmt.Printf("Array2 address: %p\n", &array2)

    // Assignment creates a complete copy
    array2 = array1

    // Modify array1
    array1[0] = 999.0

    fmt.Printf("After modification - Array1[0]: %.1f, Array2[0]: %.1f\n",
        array1[0], array2[0])

    // Array comparison
    fmt.Println("\n--- Array Comparison Test ---")
    var array3, array4 [5]int

    // Initialize with same values
    for i := 0; i < 5; i++ {
        array3[i] = i
        array4[i] = i
    }

    fmt.Printf("Arrays equal: %t\n", array3 == array4)

    // Change one value
    array4[2] = 99
    fmt.Printf("After change, arrays equal: %t\n", array3 == array4)
}
```

**Hands-on Exercise 3: When to Choose Arrays vs Slices**:

```go
// Practical examples of when to use arrays vs slices
package main

import (
    "fmt"
    "time"
    "unsafe"
)

// Use arrays for fixed-size, known-at-compile-time data
const (
    DaysInWeek = 7
    MonthsInYear = 12
    MaxRetries = 3
)

// Calendar data - perfect for arrays (fixed size)
type WeeklySchedule struct {
    tasks [DaysInWeek]string
    hours [DaysInWeek]int
}

type YearlyBudget struct {
    monthlyBudgets [MonthsInYear]float64
    monthNames     [MonthsInYear]string
}

// Retry configuration - fixed maximum attempts
type RetryConfig struct {
    delays [MaxRetries]time.Duration
    count  int
}

// Use slices for dynamic, variable-size data
type TaskList struct {
    tasks []string
    priorities []int
}

type LogBuffer struct {
    entries []string
    maxSize int
}

func main() {
    demonstrateArrayUseCases()
    demonstrateSliceUseCases()
    comparePerformance()
}

func demonstrateArrayUseCases() {
    fmt.Println("=== Array Use Cases ===")

    // Weekly schedule - fixed 7 days
    schedule := WeeklySchedule{
        tasks: [DaysInWeek]string{
            "Planning", "Development", "Testing",
            "Review", "Deployment", "Monitoring", "Rest",
        },
        hours: [DaysInWeek]int{8, 8, 8, 8, 8, 4, 0},
    }

    fmt.Println("Weekly Schedule:")
    dayNames := [DaysInWeek]string{
        "Monday", "Tuesday", "Wednesday", "Thursday",
        "Friday", "Saturday", "Sunday",
    }

    for i := 0; i < DaysInWeek; i++ {
        fmt.Printf("  %s: %s (%d hours)\n",
            dayNames[i], schedule.tasks[i], schedule.hours[i])
    }

    // Yearly budget - fixed 12 months
    budget := YearlyBudget{
        monthlyBudgets: [MonthsInYear]float64{
            10000, 12000, 11000, 13000, 14000, 15000,
            16000, 15000, 14000, 13000, 12000, 18000,
        },
        monthNames: [MonthsInYear]string{
            "Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec",
        },
    }

    total := 0.0
    for i := 0; i < MonthsInYear; i++ {
        total += budget.monthlyBudgets[i]
    }
    fmt.Printf("\nTotal yearly budget: $%.2f\n", total)

    // Retry configuration - fixed maximum attempts
    retryConfig := RetryConfig{
        delays: [MaxRetries]time.Duration{
            1 * time.Second,
            5 * time.Second,
            10 * time.Second,
        },
        count: MaxRetries,
    }

    fmt.Println("\nRetry configuration:")
    for i := 0; i < retryConfig.count; i++ {
        fmt.Printf("  Attempt %d: wait %v\n", i+1, retryConfig.delays[i])
    }
}

func demonstrateSliceUseCases() {
    fmt.Println("\n=== Slice Use Cases ===")

    // Dynamic task list
    taskList := TaskList{
        tasks:      make([]string, 0, 10),
        priorities: make([]int, 0, 10),
    }

    // Add tasks dynamically
    tasks := []struct {
        name     string
        priority int
    }{
        {"Setup environment", 5},
        {"Write tests", 7},
        {"Implement feature", 8},
        {"Code review", 6},
        {"Deploy to staging", 9},
    }

    for _, task := range tasks {
        taskList.tasks = append(taskList.tasks, task.name)
        taskList.priorities = append(taskList.priorities, task.priority)
    }

    fmt.Printf("Dynamic task list (%d tasks):\n", len(taskList.tasks))
    for i, task := range taskList.tasks {
        fmt.Printf("  %s (priority: %d)\n", task, taskList.priorities[i])
    }

    // Log buffer with dynamic growth
    logBuffer := LogBuffer{
        entries: make([]string, 0),
        maxSize: 5,
    }

    // Add log entries
    logEntries := []string{
        "Application started",
        "Database connected",
        "User logged in",
        "Processing request",
        "Request completed",
        "User logged out",
        "Application stopping",
    }

    for _, entry := range logEntries {
        logBuffer.entries = append(logBuffer.entries, entry)

        // Keep only recent entries
        if len(logBuffer.entries) > logBuffer.maxSize {
            logBuffer.entries = logBuffer.entries[1:]
        }
    }

    fmt.Printf("\nRecent log entries (max %d):\n", logBuffer.maxSize)
    for i, entry := range logBuffer.entries {
        fmt.Printf("  %d: %s\n", i+1, entry)
    }
}

func comparePerformance() {
    fmt.Println("\n=== Performance Comparison ===")

    // Array - stack allocated, predictable
    var arrayData [1000]int
    fmt.Printf("Array size: %d bytes (stack allocated)\n",
        unsafe.Sizeof(arrayData))

    // Slice - heap allocated header + backing array
    sliceData := make([]int, 1000)
    fmt.Printf("Slice header size: %d bytes (heap backing array)\n",
        unsafe.Sizeof(sliceData))

    // Memory usage
    fmt.Println("\nMemory characteristics:")
    fmt.Println("Arrays:")
    fmt.Println("  + Predictable memory usage")
    fmt.Println("  + Stack allocated (faster)")
    fmt.Println("  + No heap allocations")
    fmt.Println("  - Fixed size at compile time")
    fmt.Println("  - Large arrays can cause stack overflow")

    fmt.Println("\nSlices:")
    fmt.Println("  + Dynamic sizing")
    fmt.Println("  + Efficient append operations")
    fmt.Println("  + Can grow as needed")
    fmt.Println("  - Heap allocations (GC pressure)")
    fmt.Println("  - Indirection overhead")

    fmt.Println("\nChoose arrays when:")
    fmt.Println("  - Size is known at compile time")
    fmt.Println("  - Size is small to moderate")
    fmt.Println("  - Predictable memory usage is important")
    fmt.Println("  - Working with fixed collections (days, months, etc.)")

    fmt.Println("\nChoose slices when:")
    fmt.Println("  - Size varies at runtime")
    fmt.Println("  - Need to append/remove elements")
    fmt.Println("  - Working with user input or external data")
    fmt.Println("  - Size could be very large")
}
```

**Prerequisites**: Session 9

---

### Session 11: Slices Part 1: Declaration, Length, and Reference Types

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand slice internal structure and mechanics
- Master slice declaration and initialization patterns
- Learn the difference between length and capacity
- Apply slices effectively in automation data processing

**Videos Covered**:

- 3.5 Slices Part 1 (Declare and Length) (0:08:46)

**Key Concepts**:

- Slice header structure (pointer, length, capacity)
- nil slices vs empty slices
- Slice literals and make() function
- Length vs capacity concepts
- Reference semantics with slices

**Hands-on Exercise 1: Dynamic Log Processing with Slices**:

```go
// Dynamic log processing with slices
package main

import (
    "fmt"
    "strings"
    "time"
)

type LogProcessor struct {
    entries []string
    errors  []string
}

func NewLogProcessor() *LogProcessor {
    return &LogProcessor{
        entries: make([]string, 0, 100), // Initial capacity of 100
        errors:  make([]string, 0, 10),  // Smaller capacity for errors
    }
}

func (lp *LogProcessor) AddEntry(entry string) {
    lp.entries = append(lp.entries, entry)

    // Check for error entries
    if strings.Contains(strings.ToLower(entry), "error") {
        lp.errors = append(lp.errors, entry)
    }
}

func (lp *LogProcessor) GetStats() (int, int, int, int) {
    return len(lp.entries), cap(lp.entries), len(lp.errors), cap(lp.errors)
}

func (lp *LogProcessor) GetRecentEntries(count int) []string {
    if count > len(lp.entries) {
        count = len(lp.entries)
    }

    start := len(lp.entries) - count
    return lp.entries[start:] // Slice operation
}

func main() {
    processor := NewLogProcessor()

    // Simulate log entries
    logEntries := []string{
        "INFO: Service started",
        "DEBUG: Processing request",
        "ERROR: Database connection failed",
        "INFO: Retrying connection",
        "ERROR: Authentication failed",
        "INFO: Service running normally",
    }

    fmt.Println("Processing log entries...")
    for i, entry := range logEntries {
        processor.AddEntry(fmt.Sprintf("[%s] %s",
            time.Now().Format("15:04:05"), entry))

        entryLen, entryCap, errorLen, errorCap := processor.GetStats()
        fmt.Printf("Entry %d: entries=%d/%d, errors=%d/%d\n",
            i+1, entryLen, entryCap, errorLen, errorCap)
    }

    // Get recent entries
    recent := processor.GetRecentEntries(3)
    fmt.Printf("\nRecent entries:\n")
    for _, entry := range recent {
        fmt.Printf("  %s\n", entry)
    }

    // Show all errors
    fmt.Printf("\nError entries:\n")
    for _, error := range processor.errors {
        fmt.Printf("  %s\n", error)
    }
}
```

**Hands-on Exercise 2: Slice Internal Structure and nil vs Empty**:

```go
// Explore slice internals and different initialization patterns
package main

import (
    "fmt"
    "unsafe"
)

// SliceHeader represents the internal structure of a slice
type SliceHeader struct {
    Data uintptr
    Len  int
    Cap  int
}

func main() {
    demonstrateSliceInternals()
    demonstrateNilVsEmpty()
    demonstrateInitializationPatterns()
}

func demonstrateSliceInternals() {
    fmt.Println("=== Slice Internal Structure ===")

    // Create different slices
    var nilSlice []int
    emptySlice := []int{}
    madeSlice := make([]int, 5, 10)
    literalSlice := []int{1, 2, 3}

    // Show internal structure
    printSliceInfo("nil slice", nilSlice)
    printSliceInfo("empty slice", emptySlice)
    printSliceInfo("made slice", madeSlice)
    printSliceInfo("literal slice", literalSlice)

    fmt.Printf("Slice header size: %d bytes\n", unsafe.Sizeof(nilSlice))
}

func printSliceInfo(name string, slice []int) {
    header := (*SliceHeader)(unsafe.Pointer(&slice))
    fmt.Printf("%s: len=%d, cap=%d, data=%x\n",
        name, header.Len, header.Cap, header.Data)
}

func demonstrateNilVsEmpty() {
    fmt.Println("\n=== nil vs Empty Slices ===")

    var nilSlice []string
    emptySlice := []string{}
    madeEmpty := make([]string, 0)

    fmt.Printf("nil slice == nil: %t\n", nilSlice == nil)
    fmt.Printf("empty slice == nil: %t\n", emptySlice == nil)
    fmt.Printf("made empty == nil: %t\n", madeEmpty == nil)

    fmt.Printf("len(nil slice): %d\n", len(nilSlice))
    fmt.Printf("len(empty slice): %d\n", len(emptySlice))
    fmt.Printf("len(made empty): %d\n", len(madeEmpty))

    // All can be appended to
    nilSlice = append(nilSlice, "first")
    emptySlice = append(emptySlice, "first")
    madeEmpty = append(madeEmpty, "first")

    fmt.Printf("After append - nil slice: %v\n", nilSlice)
    fmt.Printf("After append - empty slice: %v\n", emptySlice)
    fmt.Printf("After append - made empty: %v\n", madeEmpty)

    // JSON marshaling difference
    fmt.Println("\nJSON behavior:")
    fmt.Printf("nil slice would marshal to: null\n")
    fmt.Printf("empty slice would marshal to: []\n")
}

func demonstrateInitializationPatterns() {
    fmt.Println("\n=== Slice Initialization Patterns ===")

    // Pattern 1: nil slice (zero value)
    var tasks []string
    fmt.Printf("nil slice: len=%d, cap=%d\n", len(tasks), cap(tasks))

    // Pattern 2: empty slice literal
    configs := []string{}
    fmt.Printf("empty literal: len=%d, cap=%d\n", len(configs), cap(configs))

    // Pattern 3: make with length
    buffer := make([]byte, 10)
    fmt.Printf("make with length: len=%d, cap=%d\n", len(buffer), cap(buffer))

    // Pattern 4: make with length and capacity
    queue := make([]int, 0, 20)
    fmt.Printf("make with capacity: len=%d, cap=%d\n", len(queue), cap(queue))

    // Pattern 5: slice literal with values
    priorities := []int{1, 2, 3, 4, 5}
    fmt.Printf("literal with values: len=%d, cap=%d\n", len(priorities), cap(priorities))

    // Pattern 6: slice from array
    array := [5]string{"a", "b", "c", "d", "e"}
    slice := array[1:4]
    fmt.Printf("slice from array: len=%d, cap=%d, values=%v\n",
        len(slice), cap(slice), slice)

    // Show capacity calculation from array slice
    fmt.Printf("array slice [1:4] capacity = %d (from index 1 to end)\n",
        len(array)-1)

    // Different slice expressions
    slice1 := array[:]     // full slice
    slice2 := array[1:]    // from index 1 to end
    slice3 := array[:3]    // from start to index 3
    slice4 := array[1:3]   // from index 1 to 3

    fmt.Printf("array[:] -> len=%d, cap=%d\n", len(slice1), cap(slice1))
    fmt.Printf("array[1:] -> len=%d, cap=%d\n", len(slice2), cap(slice2))
    fmt.Printf("array[:3] -> len=%d, cap=%d\n", len(slice3), cap(slice3))
    fmt.Printf("array[1:3] -> len=%d, cap=%d\n", len(slice4), cap(slice4))
}
```

**Hands-on Exercise 3: Reference Semantics and Slice Sharing**:

```go
// Demonstrate slice reference semantics and data sharing
package main

import "fmt"

type DataProcessor struct {
    rawData     []int
    processedData []int
}

func NewDataProcessor(data []int) *DataProcessor {
    return &DataProcessor{
        rawData: data, // Shares the same backing array
    }
}

func (dp *DataProcessor) ProcessData() {
    // Create a slice that shares the backing array
    dp.processedData = make([]int, len(dp.rawData))

    // Process data (double each value)
    for i, value := range dp.rawData {
        dp.processedData[i] = value * 2
    }
}

func (dp *DataProcessor) ModifyRawData(index, value int) {
    if index >= 0 && index < len(dp.rawData) {
        dp.rawData[index] = value
    }
}

func (dp *DataProcessor) GetSlice(start, end int) []int {
    if start < 0 || end > len(dp.rawData) || start > end {
        return nil
    }
    // Returns a slice that shares the backing array
    return dp.rawData[start:end]
}

func main() {
    demonstrateReferenceSemantics()
    demonstrateSliceSharing()
    demonstrateSliceModification()
}

func demonstrateReferenceSemantics() {
    fmt.Println("=== Slice Reference Semantics ===")

    // Original data
    originalData := []int{1, 2, 3, 4, 5}
    fmt.Printf("Original data: %v\n", originalData)

    // Create processor with reference to data
    processor := NewDataProcessor(originalData)

    // Modify through processor
    processor.ModifyRawData(0, 99)

    // Original data is affected because slices share backing array
    fmt.Printf("After modification through processor: %v\n", originalData)
    fmt.Printf("Processor raw data: %v\n", processor.rawData)

    // Process the data
    processor.ProcessData()
    fmt.Printf("Processed data: %v\n", processor.processedData)
}

func demonstrateSliceSharing() {
    fmt.Println("\n=== Slice Sharing and Backing Arrays ===")

    // Create original slice
    original := []int{10, 20, 30, 40, 50, 60}
    fmt.Printf("Original: %v (len=%d, cap=%d)\n",
        original, len(original), cap(original))

    // Create slices that share the backing array
    slice1 := original[1:4]  // [20, 30, 40]
    slice2 := original[2:5]  // [30, 40, 50]
    slice3 := original[:3]   // [10, 20, 30]

    fmt.Printf("slice1 [1:4]: %v (len=%d, cap=%d)\n",
        slice1, len(slice1), cap(slice1))
    fmt.Printf("slice2 [2:5]: %v (len=%d, cap=%d)\n",
        slice2, len(slice2), cap(slice2))
    fmt.Printf("slice3 [:3]: %v (len=%d, cap=%d)\n",
        slice3, len(slice3), cap(slice3))

    // Modify through slice1
    slice1[1] = 999  // This modifies original[2]

    fmt.Printf("\nAfter slice1[1] = 999:\n")
    fmt.Printf("Original: %v\n", original)
    fmt.Printf("slice1: %v\n", slice1)
    fmt.Printf("slice2: %v\n", slice2)  // slice2[0] is also affected
    fmt.Printf("slice3: %v\n", slice3)  // slice3[2] is also affected
}

func demonstrateSliceModification() {
    fmt.Println("\n=== Safe vs Unsafe Slice Operations ===")

    // Create a slice for demonstration
    data := []string{"task1", "task2", "task3", "task4", "task5"}
    fmt.Printf("Original data: %v\n", data)

    // Safe operation: create a copy
    safeCopy := make([]string, len(data))
    copy(safeCopy, data)
    safeCopy[0] = "modified_task1"

    fmt.Printf("After safe modification:\n")
    fmt.Printf("  Original: %v\n", data)
    fmt.Printf("  Safe copy: %v\n", safeCopy)

    // Unsafe operation: direct slice sharing
    unsafeSlice := data[1:4]  // Shares backing array
    unsafeSlice[0] = "modified_task2"

    fmt.Printf("After unsafe modification:\n")
    fmt.Printf("  Original: %v\n", data)  // Original is affected!
    fmt.Printf("  Unsafe slice: %v\n", unsafeSlice)

    // Demonstrate append behavior with shared slices
    fmt.Println("\n=== Append with Shared Slices ===")

    baseData := []int{1, 2, 3, 4, 5}
    fmt.Printf("Base data: %v (len=%d, cap=%d)\n",
        baseData, len(baseData), cap(baseData))

    // Create a slice with room to grow
    subSlice := baseData[:3]  // [1, 2, 3] but cap=5
    fmt.Printf("Sub slice: %v (len=%d, cap=%d)\n",
        subSlice, len(subSlice), cap(subSlice))

    // Append to sub slice - this will overwrite baseData[3]!
    subSlice = append(subSlice, 99)

    fmt.Printf("After append(subSlice, 99):\n")
    fmt.Printf("  Base data: %v\n", baseData)  // baseData[3] is now 99!
    fmt.Printf("  Sub slice: %v\n", subSlice)

    // Safe append: use full slice expression to limit capacity
    safeSlice := baseData[:3:3]  // len=3, cap=3
    fmt.Printf("\nSafe slice [0:3:3]: %v (len=%d, cap=%d)\n",
        safeSlice, len(safeSlice), cap(safeSlice))

    // Reset base data
    baseData = []int{1, 2, 3, 4, 5}
    safeSlice = baseData[:3:3]

    // This append will allocate new backing array
    safeSlice = append(safeSlice, 99)

    fmt.Printf("After safe append:\n")
    fmt.Printf("  Base data: %v (unchanged)\n", baseData)
    fmt.Printf("  Safe slice: %v (new backing array)\n", safeSlice)
}
```

**Prerequisites**: Session 10

---

### Session 12: Slices Part 2: Appending and Memory Management

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master slice growth and append mechanics
- Understand memory allocation patterns with slices
- Learn efficient slice management techniques
- Optimize slice usage for automation workloads

**Videos Covered**:

- 3.6 Slices Part 2 (Appending Slices) (0:15:32)

**Key Concepts**:

- append() function and slice growth
- Capacity doubling and memory allocation
- Slice reallocation and pointer invalidation
- Pre-allocation strategies for performance
- Memory efficiency with large slices

**Hands-on Exercise 1: Efficient Batch Processing with Slice Management**:

```go
// Efficient batch processing with slice management
package main

import (
    "fmt"
    "runtime"
)

type BatchProcessor struct {
    items    []string
    batches  [][]string
    batchSize int
}

func NewBatchProcessor(batchSize int) *BatchProcessor {
    return &BatchProcessor{
        items:     make([]string, 0, batchSize*10), // Pre-allocate
        batches:   make([][]string, 0, 10),
        batchSize: batchSize,
    }
}

func (bp *BatchProcessor) AddItem(item string) {
    bp.items = append(bp.items, item)

    // Create batch when we reach batch size
    if len(bp.items) >= bp.batchSize {
        bp.createBatch()
    }
}

func (bp *BatchProcessor) createBatch() {
    if len(bp.items) == 0 {
        return
    }

    // Create new slice for batch (copy to avoid sharing)
    batch := make([]string, len(bp.items))
    copy(batch, bp.items)

    bp.batches = append(bp.batches, batch)

    // Reset items slice but keep capacity
    bp.items = bp.items[:0]
}

func (bp *BatchProcessor) ProcessBatches() {
    // Process any remaining items
    if len(bp.items) > 0 {
        bp.createBatch()
    }

    fmt.Printf("Processing %d batches...\n", len(bp.batches))

    for i, batch := range bp.batches {
        fmt.Printf("Batch %d: %d items\n", i+1, len(batch))

        // Simulate processing
        for j, item := range batch {
            if j < 3 { // Show first 3 items
                fmt.Printf("  Processing: %s\n", item)
            } else if j == 3 {
                fmt.Printf("  ... and %d more items\n", len(batch)-3)
                break
            }
        }
    }
}

func (bp *BatchProcessor) GetMemoryStats() {
    var m runtime.MemStats
    runtime.ReadMemStats(&m)

    fmt.Printf("Memory stats:\n")
    fmt.Printf("  Items slice - len: %d, cap: %d\n", len(bp.items), cap(bp.items))
    fmt.Printf("  Batches - count: %d, cap: %d\n", len(bp.batches), cap(bp.batches))
    fmt.Printf("  Heap alloc: %d KB\n", m.HeapAlloc/1024)
}

func main() {
    processor := NewBatchProcessor(5)

    // Add items to processor
    for i := 0; i < 23; i++ {
        item := fmt.Sprintf("automation-task-%03d", i)
        processor.AddItem(item)

        if i%5 == 4 { // Every 5 items
            processor.GetMemoryStats()
            fmt.Println()
        }
    }

    // Process all batches
    processor.ProcessBatches()

    // Final memory stats
    fmt.Println("\nFinal stats:")
    processor.GetMemoryStats()
}
```

**Hands-on Exercise 2: Slice Growth Patterns and Memory Allocation**:

```go
// Demonstrate slice growth patterns and memory allocation strategies
package main

import (
    "fmt"
    "runtime"
)

func main() {
    demonstrateSliceGrowth()
    demonstratePreallocation()
    demonstrateMemoryEfficiency()
}

func demonstrateSliceGrowth() {
    fmt.Println("=== Slice Growth Patterns ===")

    var slice []int
    fmt.Printf("Initial: len=%d, cap=%d\n", len(slice), cap(slice))

    // Track capacity changes during growth
    for i := 0; i < 20; i++ {
        oldCap := cap(slice)
        slice = append(slice, i)
        newCap := cap(slice)

        if newCap != oldCap {
            fmt.Printf("After append(%d): len=%d, cap=%d (grew from %d)\n",
                i, len(slice), newCap, oldCap)
        }
    }

    // Show the growth pattern
    fmt.Println("\nGrowth pattern analysis:")
    fmt.Println("- Capacity starts at 0")
    fmt.Println("- First allocation: cap=1")
    fmt.Println("- Then doubles: 1→2→4→8→16→32...")
    fmt.Println("- Growth factor may vary for large slices")
}

func demonstratePreallocation() {
    fmt.Println("\n=== Pre-allocation vs Dynamic Growth ===")

    const itemCount = 10000

    // Method 1: Dynamic growth (inefficient)
    var m1 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)

    var dynamicSlice []int
    for i := 0; i < itemCount; i++ {
        dynamicSlice = append(dynamicSlice, i)
    }

    var m2 runtime.MemStats
    runtime.ReadMemStats(&m2)
    dynamicAllocs := m2.Mallocs - m1.Mallocs

    // Method 2: Pre-allocation (efficient)
    runtime.GC()
    runtime.ReadMemStats(&m1)

    preallocSlice := make([]int, 0, itemCount)
    for i := 0; i < itemCount; i++ {
        preallocSlice = append(preallocSlice, i)
    }

    runtime.ReadMemStats(&m2)
    preallocAllocs := m2.Mallocs - m1.Mallocs

    fmt.Printf("Dynamic growth: %d allocations\n", dynamicAllocs)
    fmt.Printf("Pre-allocation: %d allocations\n", preallocAllocs)
    fmt.Printf("Allocation reduction: %.1fx\n",
        float64(dynamicAllocs)/float64(preallocAllocs))

    // Method 3: Pre-sized slice
    runtime.GC()
    runtime.ReadMemStats(&m1)

    presizedSlice := make([]int, itemCount)
    for i := 0; i < itemCount; i++ {
        presizedSlice[i] = i
    }

    runtime.ReadMemStats(&m2)
    presizedAllocs := m2.Mallocs - m1.Mallocs

    fmt.Printf("Pre-sized slice: %d allocations\n", presizedAllocs)
}

func demonstrateMemoryEfficiency() {
    fmt.Println("\n=== Memory Efficiency Strategies ===")

    // Strategy 1: Reuse slices by resetting length
    reusableSlice := make([]string, 0, 100)

    for batch := 0; batch < 3; batch++ {
        fmt.Printf("\nBatch %d:\n", batch+1)

        // Add items to slice
        for i := 0; i < 5; i++ {
            item := fmt.Sprintf("batch%d-item%d", batch+1, i+1)
            reusableSlice = append(reusableSlice, item)
        }

        fmt.Printf("  After adding: len=%d, cap=%d\n",
            len(reusableSlice), cap(reusableSlice))

        // Process items (simulate)
        fmt.Printf("  Processing %d items...\n", len(reusableSlice))

        // Reset slice for reuse (keep capacity)
        reusableSlice = reusableSlice[:0]
        fmt.Printf("  After reset: len=%d, cap=%d\n",
            len(reusableSlice), cap(reusableSlice))
    }

    // Strategy 2: Efficient copying
    fmt.Println("\n=== Efficient Copying Strategies ===")

    source := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

    // Method 1: Using copy() function
    dest1 := make([]int, len(source))
    copy(dest1, source)
    fmt.Printf("copy() method: %v\n", dest1)

    // Method 2: Using append with empty slice
    var dest2 []int
    dest2 = append(dest2, source...)
    fmt.Printf("append() method: %v\n", dest2)

    // Method 3: Manual copying (for partial copies)
    dest3 := make([]int, 5)
    for i := 0; i < 5; i++ {
        dest3[i] = source[i]
    }
    fmt.Printf("manual copy (first 5): %v\n", dest3)

    // Strategy 3: Avoiding memory leaks with large slices
    fmt.Println("\n=== Avoiding Memory Leaks ===")

    largeSlice := make([]byte, 1000000) // 1MB
    for i := range largeSlice {
        largeSlice[i] = byte(i % 256)
    }

    // BAD: This keeps the entire 1MB in memory
    badSubslice := largeSlice[0:10]
    fmt.Printf("Bad subslice: len=%d, cap=%d (keeps %d bytes)\n",
        len(badSubslice), cap(badSubslice), cap(badSubslice))

    // GOOD: Copy only what you need
    goodSubslice := make([]byte, 10)
    copy(goodSubslice, largeSlice[0:10])
    fmt.Printf("Good subslice: len=%d, cap=%d (uses %d bytes)\n",
        len(goodSubslice), cap(goodSubslice), cap(goodSubslice))

    // Now largeSlice can be garbage collected
    largeSlice = nil
    runtime.GC()

    fmt.Println("\nMemory efficiency tips:")
    fmt.Println("- Pre-allocate when size is known")
    fmt.Println("- Reuse slices by resetting length")
    fmt.Println("- Copy subslices from large slices")
    fmt.Println("- Use copy() for safe copying")
    fmt.Println("- Set large slices to nil when done")
}
```

**Hands-on Exercise 3: Advanced Append Patterns and Performance**:

```go
// Advanced append patterns for high-performance automation
package main

import (
    "fmt"
    "time"
)

type PerformanceTracker struct {
    operations []string
    timings    []time.Duration
    results    []bool
}

func NewPerformanceTracker() *PerformanceTracker {
    return &PerformanceTracker{
        operations: make([]string, 0, 1000),
        timings:    make([]time.Duration, 0, 1000),
        results:    make([]bool, 0, 1000),
    }
}

func (pt *PerformanceTracker) TrackOperation(name string, fn func() bool) {
    start := time.Now()
    result := fn()
    duration := time.Since(start)

    pt.operations = append(pt.operations, name)
    pt.timings = append(pt.timings, duration)
    pt.results = append(pt.results, result)
}

func (pt *PerformanceTracker) GetSummary() (int, int, time.Duration) {
    successful := 0
    var totalTime time.Duration

    for i, result := range pt.results {
        if result {
            successful++
        }
        totalTime += pt.timings[i]
    }

    return successful, len(pt.results), totalTime
}

func (pt *PerformanceTracker) GetFailures() []string {
    var failures []string

    for i, result := range pt.results {
        if !result {
            failures = append(failures, pt.operations[i])
        }
    }

    return failures
}

// Demonstrate different append patterns
func main() {
    demonstrateAppendPatterns()
    demonstrateSliceCapacityManagement()
    demonstrateBulkOperations()
}

func demonstrateAppendPatterns() {
    fmt.Println("=== Append Patterns ===")

    // Pattern 1: Single element append
    var single []int
    for i := 0; i < 5; i++ {
        single = append(single, i)
    }
    fmt.Printf("Single append: %v\n", single)

    // Pattern 2: Multiple element append
    var multiple []int
    multiple = append(multiple, 1, 2, 3, 4, 5)
    fmt.Printf("Multiple append: %v\n", multiple)

    // Pattern 3: Slice append (variadic)
    var slice1 []int = []int{1, 2, 3}
    var slice2 []int = []int{4, 5, 6}
    combined := append(slice1, slice2...)
    fmt.Printf("Slice append: %v\n", combined)

    // Pattern 4: Conditional append
    var filtered []int
    source := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
    for _, v := range source {
        if v%2 == 0 { // Even numbers only
            filtered = append(filtered, v)
        }
    }
    fmt.Printf("Conditional append: %v\n", filtered)

    // Pattern 5: Append with transformation
    var transformed []string
    numbers := []int{1, 2, 3, 4, 5}
    for _, num := range numbers {
        transformed = append(transformed, fmt.Sprintf("item-%d", num))
    }
    fmt.Printf("Transform append: %v\n", transformed)
}

func demonstrateSliceCapacityManagement() {
    fmt.Println("\n=== Capacity Management ===")

    // Demonstrate capacity growth and reallocation
    tracker := NewPerformanceTracker()

    // Simulate various operations
    operations := []struct {
        name string
        fn   func() bool
    }{
        {"database_connect", func() bool { time.Sleep(10 * time.Millisecond); return true }},
        {"api_call_1", func() bool { time.Sleep(5 * time.Millisecond); return true }},
        {"file_process", func() bool { time.Sleep(15 * time.Millisecond); return false }},
        {"api_call_2", func() bool { time.Sleep(8 * time.Millisecond); return true }},
        {"cleanup", func() bool { time.Sleep(3 * time.Millisecond); return true }},
    }

    fmt.Println("Tracking operations...")
    for _, op := range operations {
        tracker.TrackOperation(op.name, op.fn)
        fmt.Printf("  %s: len=%d, cap=%d\n",
            op.name, len(tracker.operations), cap(tracker.operations))
    }

    successful, total, totalTime := tracker.GetSummary()
    fmt.Printf("\nSummary: %d/%d successful, total time: %v\n",
        successful, total, totalTime)

    failures := tracker.GetFailures()
    if len(failures) > 0 {
        fmt.Printf("Failures: %v\n", failures)
    }
}

func demonstrateBulkOperations() {
    fmt.Println("\n=== Bulk Operations ===")

    // Efficient bulk append vs individual appends
    const itemCount = 1000

    // Method 1: Individual appends (less efficient)
    start := time.Now()
    var individual []int
    for i := 0; i < itemCount; i++ {
        individual = append(individual, i)
    }
    individualTime := time.Since(start)

    // Method 2: Bulk append with pre-allocation
    start = time.Now()
    bulk := make([]int, 0, itemCount)
    batch := make([]int, 100) // Prepare batch
    for i := 0; i < itemCount; i += 100 {
        // Fill batch
        batchSize := 100
        if i+100 > itemCount {
            batchSize = itemCount - i
        }

        for j := 0; j < batchSize; j++ {
            batch[j] = i + j
        }

        // Bulk append
        bulk = append(bulk, batch[:batchSize]...)
    }
    bulkTime := time.Since(start)

    fmt.Printf("Individual appends: %v\n", individualTime)
    fmt.Printf("Bulk appends: %v\n", bulkTime)
    fmt.Printf("Bulk is %.2fx faster\n",
        float64(individualTime)/float64(bulkTime))

    // Demonstrate append with capacity monitoring
    fmt.Println("\n=== Capacity Monitoring ===")

    var monitored []string
    capacityChanges := 0
    lastCap := cap(monitored)

    for i := 0; i < 20; i++ {
        monitored = append(monitored, fmt.Sprintf("item-%d", i))

        currentCap := cap(monitored)
        if currentCap != lastCap {
            capacityChanges++
            fmt.Printf("Capacity changed at item %d: %d → %d\n",
                i, lastCap, currentCap)
            lastCap = currentCap
        }
    }

    fmt.Printf("Total capacity changes: %d\n", capacityChanges)
    fmt.Printf("Final: len=%d, cap=%d\n", len(monitored), cap(monitored))

    // Show memory efficiency of different approaches
    fmt.Println("\n=== Memory Efficiency Comparison ===")

    // Approach 1: No pre-allocation
    var noPrealloc []int
    for i := 0; i < 100; i++ {
        noPrealloc = append(noPrealloc, i)
    }
    fmt.Printf("No pre-allocation: len=%d, cap=%d, efficiency=%.1f%%\n",
        len(noPrealloc), cap(noPrealloc),
        float64(len(noPrealloc))/float64(cap(noPrealloc))*100)

    // Approach 2: Exact pre-allocation
    exactPrealloc := make([]int, 0, 100)
    for i := 0; i < 100; i++ {
        exactPrealloc = append(exactPrealloc, i)
    }
    fmt.Printf("Exact pre-allocation: len=%d, cap=%d, efficiency=%.1f%%\n",
        len(exactPrealloc), cap(exactPrealloc),
        float64(len(exactPrealloc))/float64(cap(exactPrealloc))*100)

    // Approach 3: Over pre-allocation
    overPrealloc := make([]int, 0, 200)
    for i := 0; i < 100; i++ {
        overPrealloc = append(overPrealloc, i)
    }
    fmt.Printf("Over pre-allocation: len=%d, cap=%d, efficiency=%.1f%%\n",
        len(overPrealloc), cap(overPrealloc),
        float64(len(overPrealloc))/float64(cap(overPrealloc))*100)
}
```

**Prerequisites**: Session 11

---

### Session 13: Slices Part 3: Slicing Operations and References

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master slice operations and sub-slicing
- Understand slice sharing and reference behavior
- Learn safe slicing practices to avoid memory leaks
- Apply advanced slicing techniques in automation

**Videos Covered**:

- 3.7 Slices Part 3 (Taking Slices of Slices) (0:11:45)
- 3.8 Slices Part 4 (Slices and References) (0:05:51)

**Key Concepts**:

- Slice expressions and bounds checking
- Shared backing arrays and memory implications
- Copy vs slice operations
- Memory leaks with large slice references
- Safe slicing patterns

**Hands-on Exercise**:

```go
// Safe data windowing for time-series automation
package main

import (
    "fmt"
    "time"
)

type TimeSeriesData struct {
    timestamps []int64
    values     []float64
}

func NewTimeSeriesData(capacity int) *TimeSeriesData {
    return &TimeSeriesData{
        timestamps: make([]int64, 0, capacity),
        values:     make([]float64, 0, capacity),
    }
}

func (ts *TimeSeriesData) AddPoint(timestamp int64, value float64) {
    ts.timestamps = append(ts.timestamps, timestamp)
    ts.values = append(ts.values, value)
}

// Safe windowing - creates copy to avoid memory leaks
func (ts *TimeSeriesData) GetWindow(start, end int) *TimeSeriesData {
    if start < 0 || end > len(ts.timestamps) || start >= end {
        return NewTimeSeriesData(0)
    }

    window := NewTimeSeriesData(end - start)

    // Copy data instead of sharing slice
    for i := start; i < end; i++ {
        window.AddPoint(ts.timestamps[i], ts.values[i])
    }

    return window
}

// Unsafe windowing - shares backing array (for comparison)
func (ts *TimeSeriesData) GetWindowUnsafe(start, end int) *TimeSeriesData {
    if start < 0 || end > len(ts.timestamps) || start >= end {
        return NewTimeSeriesData(0)
    }

    return &TimeSeriesData{
        timestamps: ts.timestamps[start:end], // Shares backing array
        values:     ts.values[start:end],     // Shares backing array
    }
}

func (ts *TimeSeriesData) CalculateAverage() float64 {
    if len(ts.values) == 0 {
        return 0
    }

    var sum float64
    for _, value := range ts.values {
        sum += value
    }

    return sum / float64(len(ts.values))
}

func (ts *TimeSeriesData) Size() int {
    return len(ts.timestamps)
}

func main() {
    // Create large time series dataset
    data := NewTimeSeriesData(10000)

    // Add sample data points
    baseTime := time.Now().Unix()
    for i := 0; i < 1000; i++ {
        timestamp := baseTime + int64(i*60) // Every minute
        value := 50.0 + float64(i%100)     // Varying values
        data.AddPoint(timestamp, value)
    }

    fmt.Printf("Original dataset size: %d points\n", data.Size())

    // Get safe window (last 100 points)
    safeWindow := data.GetWindow(900, 1000)
    fmt.Printf("Safe window size: %d points\n", safeWindow.Size())
    fmt.Printf("Safe window average: %.2f\n", safeWindow.CalculateAverage())

    // Get unsafe window (for comparison)
    unsafeWindow := data.GetWindowUnsafe(900, 1000)
    fmt.Printf("Unsafe window size: %d points\n", unsafeWindow.Size())
    fmt.Printf("Unsafe window average: %.2f\n", unsafeWindow.CalculateAverage())

    // Demonstrate slice sharing issue
    fmt.Println("\nDemonstrating slice sharing:")

    // Modify original data
    data.timestamps[950] = 999999
    data.values[950] = -1.0

    fmt.Printf("After modifying original data:\n")
    fmt.Printf("Safe window average: %.2f (unchanged)\n", safeWindow.CalculateAverage())
    fmt.Printf("Unsafe window average: %.2f (changed!)\n", unsafeWindow.CalculateAverage())
}
```

**Prerequisites**: Session 12

---

### Session 14: Strings, Maps, and Range Mechanics

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master string operations and UTF-8 handling
- Understand map operations and performance characteristics
- Learn range mechanics for different data types
- Apply strings and maps in automation configuration

**Videos Covered**:

- 3.9 Slices Part 5 (Strings and Slices) (0:08:29)
- 3.10 Slices Part 6 (Range Mechanics) (0:04:35)
- 3.11 Maps (0:08:03)

**Key Concepts**:

- String immutability and UTF-8 encoding
- String to []byte conversions and performance
- Map declaration, initialization, and operations
- Map performance characteristics and hash collisions
- Range over strings, slices, arrays, and maps

**Hands-on Exercise**:

```go
// Configuration management with strings and maps
package main

import (
    "fmt"
    "strings"
    "unicode/utf8"
)

type ConfigManager struct {
    settings map[string]string
    metadata map[string]map[string]interface{}
}

func NewConfigManager() *ConfigManager {
    return &ConfigManager{
        settings: make(map[string]string),
        metadata: make(map[string]map[string]interface{}),
    }
}

func (cm *ConfigManager) SetConfig(key, value string) {
    cm.settings[key] = value

    // Store metadata about the configuration
    if cm.metadata[key] == nil {
        cm.metadata[key] = make(map[string]interface{})
    }

    cm.metadata[key]["length"] = utf8.RuneCountInString(value)
    cm.metadata[key]["bytes"] = len(value)
    cm.metadata[key]["type"] = cm.inferType(value)
}

func (cm *ConfigManager) inferType(value string) string {
    value = strings.TrimSpace(value)

    if value == "true" || value == "false" {
        return "boolean"
    }

    if strings.Contains(value, ".") {
        return "float"
    }

    // Check if it's a number
    for _, r := range value {
        if r < '0' || r > '9' {
            return "string"
        }
    }

    return "integer"
}

func (cm *ConfigManager) GetConfig(key string) (string, bool) {
    value, exists := cm.settings[key]
    return value, exists
}

func (cm *ConfigManager) ListConfigs() {
    fmt.Println("Configuration Settings:")

    // Range over map
    for key, value := range cm.settings {
        meta := cm.metadata[key]
        fmt.Printf("  %s = %s [type: %s, runes: %d, bytes: %d]\n",
            key, value, meta["type"], meta["length"], meta["bytes"])
    }
}

func (cm *ConfigManager) SearchConfigs(pattern string) []string {
    var matches []string

    pattern = strings.ToLower(pattern)

    for key, value := range cm.settings {
        // Search in key
        if strings.Contains(strings.ToLower(key), pattern) {
            matches = append(matches, key)
            continue
        }

        // Search in value
        if strings.Contains(strings.ToLower(value), pattern) {
            matches = append(matches, key)
        }
    }

    return matches
}

func (cm *ConfigManager) ProcessStringData(data string) map[string]int {
    wordCount := make(map[string]int)

    // Range over string (by rune)
    words := strings.Fields(data)
    for _, word := range words {
        // Clean word and count
        word = strings.ToLower(strings.Trim(word, ".,!?"))
        if word != "" {
            wordCount[word]++
        }
    }

    return wordCount
}

func main() {
    config := NewConfigManager()

    // Set various configuration values
    config.SetConfig("server_port", "8080")
    config.SetConfig("database_url", "postgresql://localhost:5432/mydb")
    config.SetConfig("enable_logging", "true")
    config.SetConfig("max_connections", "100")
    config.SetConfig("timeout_seconds", "30.5")
    config.SetConfig("service_name", "Automation Service 🚀")

    // List all configurations
    config.ListConfigs()

    // Search configurations
    fmt.Println("\nSearching for 'server':")
    matches := config.SearchConfigs("server")
    for _, match := range matches {
        if value, exists := config.GetConfig(match); exists {
            fmt.Printf("  Found: %s = %s\n", match, value)
        }
    }

    // Process text data
    logData := "Error connecting to database. Retrying connection. Connection successful."
    wordStats := config.ProcessStringData(logData)

    fmt.Println("\nWord frequency in log data:")
    for word, count := range wordStats {
        fmt.Printf("  '%s': %d\n", word, count)
    }
}
```

**Prerequisites**: Session 13

---

### Session 15: Methods Part 1: Declaration and Receiver Behavior

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand the transition from concrete data to behavior-driven design
- Master method declaration syntax and receiver concepts
- Learn when data should have behavior vs using functions
- Apply method concepts to automation service design
- Understand the importance of semantic consistency

**Videos Covered**:

- 4.1 Methods Part 1 (Declare & Receiver Behavior) (0:35:00)

**Key Concepts**:

- Decoupling through behavior: moving from concrete to abstract
- Methods vs functions: when to choose each approach
- Receiver types: value receivers vs pointer receivers
- Semantic consistency: data drives the semantic model
- Method naming conventions and receiver naming
- Go's automatic receiver adjustment during method calls

**Hands-on Exercise**:

```go
// Automation service with method-based behavior
package main

import (
    "fmt"
    "log"
    "time"
)

// ServiceConfig represents automation service configuration
type ServiceConfig struct {
    Name        string
    Port        int
    MaxRetries  int
    Timeout     time.Duration
    EnableDebug bool
}

// Value receiver - operates on copy, used for read-only operations
func (sc ServiceConfig) GetConnectionString() string {
    return fmt.Sprintf("%s:localhost:%d", sc.Name, sc.Port)
}

// Value receiver - validation doesn't need to modify data
func (sc ServiceConfig) IsValid() bool {
    return sc.Name != "" && sc.Port > 0 && sc.Port < 65536
}

// Pointer receiver - modifies the configuration
func (sc *ServiceConfig) UpdateTimeout(timeout time.Duration) {
    sc.Timeout = timeout
    if sc.EnableDebug {
        log.Printf("Updated timeout for %s to %v", sc.Name, timeout)
    }
}

// Pointer receiver - enables/disables debug mode
func (sc *ServiceConfig) SetDebugMode(enabled bool) {
    sc.EnableDebug = enabled
    if enabled {
        log.Printf("Debug mode enabled for service: %s", sc.Name)
    }
}

// AutomationTask represents a task in the automation pipeline
type AutomationTask struct {
    ID          string
    Description string
    Status      string
    CreatedAt   time.Time
    CompletedAt *time.Time
}

// Value receiver - read-only status check
func (at AutomationTask) IsCompleted() bool {
    return at.Status == "completed" && at.CompletedAt != nil
}

// Value receiver - calculate duration
func (at AutomationTask) Duration() time.Duration {
    if at.CompletedAt == nil {
        return time.Since(at.CreatedAt)
    }
    return at.CompletedAt.Sub(at.CreatedAt)
}

// Pointer receiver - mark task as completed
func (at *AutomationTask) MarkCompleted() {
    now := time.Now()
    at.Status = "completed"
    at.CompletedAt = &now
}

// Pointer receiver - update task status
func (at *AutomationTask) UpdateStatus(status string) {
    at.Status = status
    log.Printf("Task %s status updated to: %s", at.ID, status)
}

func main() {
    // Create service configuration using value semantics
    config := ServiceConfig{
        Name:        "DataProcessor",
        Port:        8080,
        MaxRetries:  3,
        Timeout:     30 * time.Second,
        EnableDebug: false,
    }

    // Demonstrate value receiver calls
    fmt.Printf("Service: %s\n", config.GetConnectionString())
    fmt.Printf("Config valid: %t\n", config.IsValid())

    // Demonstrate pointer receiver calls
    // Go automatically takes address of config for pointer receiver methods
    config.SetDebugMode(true)
    config.UpdateTimeout(45 * time.Second)

    // Create automation task
    task := AutomationTask{
        ID:          "TASK-001",
        Description: "Process daily reports",
        Status:      "running",
        CreatedAt:   time.Now().Add(-5 * time.Minute),
    }

    fmt.Printf("\nTask %s - Completed: %t\n", task.ID, task.IsCompleted())
    fmt.Printf("Task duration: %v\n", task.Duration())

    // Complete the task
    task.MarkCompleted()
    fmt.Printf("Task %s - Completed: %t\n", task.ID, task.IsCompleted())
    fmt.Printf("Final duration: %v\n", task.Duration())

    // Demonstrate working with pointer to task
    newTask := &AutomationTask{
        ID:          "TASK-002",
        Description: "Backup database",
        Status:      "pending",
        CreatedAt:   time.Now(),
    }

    // Go automatically dereferences pointer for value receiver methods
    fmt.Printf("\nNew task completed: %t\n", newTask.IsCompleted())
    newTask.UpdateStatus("running")
}
```

**Prerequisites**: Session 14

---

### Session 16: Methods Part 2: Value vs Pointer Semantics

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master semantic consistency principles for different type classes
- Understand when to use value vs pointer semantics for methods
- Learn standard library patterns and semantic guidelines
- Apply semantic consistency to automation system design
- Recognize exceptions and when they're appropriate

**Videos Covered**:

- 4.1 Methods Part 2 (Value & Pointer Semantics) (0:28:15)

**Key Concepts**:

- Three classes of types: built-in, reference, and user-defined
- Built-in types (numerics, strings, bools): always use value semantics
- Reference types (slices, maps, channels, interfaces): use value semantics
- User-defined struct types: choose based on data characteristics
- Type drives semantics, not the operation
- Mutation APIs can use value semantics (sandbox pattern)
- Standard library semantic consistency patterns

**Hands-on Exercise**:

```go
// Semantic consistency in automation monitoring system
package main

import (
    "fmt"
    "strings"
    "time"
)

// Built-in type wrapper - uses value semantics
type ServiceID string

// Value receiver - built-in types always use value semantics
func (sid ServiceID) IsValid() bool {
    return len(sid) > 0 && !strings.Contains(string(sid), " ")
}

// Value receiver - returns new value (immutable pattern)
func (sid ServiceID) WithPrefix(prefix string) ServiceID {
    return ServiceID(prefix + string(sid))
}

// Reference type wrapper - uses value semantics
type MetricValues []float64

// Value receiver - reference types use value semantics
func (mv MetricValues) Average() float64 {
    if len(mv) == 0 {
        return 0
    }

    var sum float64
    for _, value := range mv {
        sum += value
    }
    return sum / float64(len(mv))
}

// Value receiver - mutation API using value semantics (sandbox pattern)
func (mv MetricValues) Normalize() MetricValues {
    if len(mv) == 0 {
        return mv
    }

    // Create new slice (mutation in isolation)
    normalized := make(MetricValues, len(mv))
    max := mv.Max()

    for i, value := range mv {
        normalized[i] = value / max
    }

    return normalized // Return new value
}

// Value receiver - helper method
func (mv MetricValues) Max() float64 {
    if len(mv) == 0 {
        return 0
    }

    max := mv[0]
    for _, value := range mv {
        if value > max {
            max = value
        }
    }
    return max
}

// User-defined struct type - choose semantics based on data nature
type MonitoringSession struct {
    ID        ServiceID
    StartTime time.Time
    Metrics   MetricValues
    Active    bool
}

// Value receiver - read-only operations for small, simple structs
func (ms MonitoringSession) Duration() time.Duration {
    return time.Since(ms.StartTime)
}

// Value receiver - validation doesn't require mutation
func (ms MonitoringSession) IsHealthy() bool {
    if !ms.Active || len(ms.Metrics) == 0 {
        return false
    }

    avg := ms.Metrics.Average()
    return avg > 0.5 && avg < 0.95 // Healthy range
}

// Large struct type - uses pointer semantics for efficiency
type AutomationPipeline struct {
    ID          ServiceID
    Name        string
    Sessions    []MonitoringSession
    Config      map[string]string
    Statistics  struct {
        TotalRuns    int64
        SuccessCount int64
        FailureCount int64
        LastRun      time.Time
    }
    CreatedAt time.Time
    UpdatedAt time.Time
}

// Pointer receiver - large structs use pointer semantics
func (ap *AutomationPipeline) AddSession(session MonitoringSession) {
    ap.Sessions = append(ap.Sessions, session)
    ap.UpdatedAt = time.Now()
}

// Pointer receiver - mutation requires pointer semantics
func (ap *AutomationPipeline) RecordRun(success bool) {
    ap.Statistics.TotalRuns++
    if success {
        ap.Statistics.SuccessCount++
    } else {
        ap.Statistics.FailureCount++
    }
    ap.Statistics.LastRun = time.Now()
    ap.UpdatedAt = time.Now()
}

// Pointer receiver - consistent with struct semantics
func (ap *AutomationPipeline) SuccessRate() float64 {
    if ap.Statistics.TotalRuns == 0 {
        return 0
    }
    return float64(ap.Statistics.SuccessCount) / float64(ap.Statistics.TotalRuns)
}

func main() {
    // Built-in type semantics
    serviceID := ServiceID("web-service")
    fmt.Printf("Service ID valid: %t\n", serviceID.IsValid())
    prefixedID := serviceID.WithPrefix("prod-")
    fmt.Printf("Prefixed ID: %s\n", prefixedID)

    // Reference type semantics
    metrics := MetricValues{0.1, 0.8, 0.6, 0.9, 0.7}
    fmt.Printf("Average: %.2f\n", metrics.Average())

    // Mutation in isolation (value semantics)
    normalized := metrics.Normalize()
    fmt.Printf("Original max: %.2f\n", metrics.Max())
    fmt.Printf("Normalized max: %.2f\n", normalized.Max())

    // Small struct with value semantics
    session := MonitoringSession{
        ID:        serviceID,
        StartTime: time.Now().Add(-5 * time.Minute),
        Metrics:   metrics,
        Active:    true,
    }

    fmt.Printf("Session duration: %v\n", session.Duration())
    fmt.Printf("Session healthy: %t\n", session.IsHealthy())

    // Large struct with pointer semantics
    pipeline := &AutomationPipeline{
        ID:        ServiceID("pipeline-001"),
        Name:      "Daily Processing Pipeline",
        Config:    make(map[string]string),
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
    }

    // Add sessions and record runs
    pipeline.AddSession(session)
    pipeline.RecordRun(true)
    pipeline.RecordRun(true)
    pipeline.RecordRun(false)

    fmt.Printf("Pipeline success rate: %.2f%%\n", pipeline.SuccessRate()*100)
    fmt.Printf("Total sessions: %d\n", len(pipeline.Sessions))
}
```

**Prerequisites**: Session 15

---

### Session 17: Methods Part 3: Function Variables and Method Sets

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand methods as syntactic sugar over functions
- Master function variables and method values
- Learn about method sets and type behavior inheritance
- Understand the cost of decoupling through function variables
- Apply function variables in automation callback patterns

**Videos Covered**:

- 4.1 Methods Part 3 (Function Method Variables) (0:18:30)

**Key Concepts**:

- Methods are syntactic sugar: receiver is the first parameter
- Function variables: methods can be assigned to variables
- Method values vs method expressions
- Semantic consistency prevents mixing value/pointer semantics
- Function variables create indirection and potential allocations
- Named types don't inherit behavior from underlying types
- Decoupling code and data through function variables

**Hands-on Exercise**:

```go
// Function variables and method sets in automation system
package main

import (
    "fmt"
    "log"
    "time"
)

// TaskProcessor represents different ways to process automation tasks
type TaskProcessor struct {
    Name     string
    Priority int
    Active   bool
}

// Value receiver - read-only operation
func (tp TaskProcessor) GetInfo() string {
    status := "inactive"
    if tp.Active {
        status = "active"
    }
    return fmt.Sprintf("Processor: %s (Priority: %d, Status: %s)",
        tp.Name, tp.Priority, status)
}

// Pointer receiver - modifies state
func (tp *TaskProcessor) Activate() {
    tp.Active = true
    log.Printf("Activated processor: %s", tp.Name)
}

// Pointer receiver - processes task
func (tp *TaskProcessor) ProcessTask(taskID string) error {
    if !tp.Active {
        return fmt.Errorf("processor %s is not active", tp.Name)
    }

    log.Printf("Processing task %s with %s", taskID, tp.Name)
    time.Sleep(100 * time.Millisecond) // Simulate processing
    return nil
}

// ProcessorFunc represents a function that can process tasks
type ProcessorFunc func(string) error

// AutomationPipeline manages task processing with different strategies
type AutomationPipeline struct {
    Name       string
    Processors []TaskProcessor
    Strategy   ProcessorFunc // Function variable for decoupling
}

// Method that returns a function variable (method value)
func (ap *AutomationPipeline) GetProcessorStrategy(processorName string) ProcessorFunc {
    // Find the processor
    for i := range ap.Processors {
        if ap.Processors[i].Name == processorName {
            processor := &ap.Processors[i] // Get pointer for method binding

            // Return method value - binds method to specific processor instance
            return processor.ProcessTask
        }
    }

    // Return default strategy
    return func(taskID string) error {
        return fmt.Errorf("no processor found for task %s", taskID)
    }
}

// Execute tasks using the configured strategy
func (ap *AutomationPipeline) ExecuteTasks(tasks []string) {
    if ap.Strategy == nil {
        log.Println("No processing strategy configured")
        return
    }

    for _, task := range tasks {
        if err := ap.Strategy(task); err != nil {
            log.Printf("Error processing task %s: %v", task, err)
        }
    }
}

// Custom type based on TaskProcessor (no behavior inheritance)
type SpecializedProcessor TaskProcessor

// This type has NO methods - behavior is not inherited
// We must define methods explicitly if needed

func (sp SpecializedProcessor) GetSpecialInfo() string {
    // Convert to underlying type to access fields
    return fmt.Sprintf("Specialized: %s", sp.Name)
}

// Demonstration of method expressions vs method values
func demonstrateMethodTypes() {
    processor := TaskProcessor{
        Name:     "DataProcessor",
        Priority: 1,
        Active:   false,
    }

    fmt.Println("\n=== Method Values vs Method Expressions ===")

    // Method value - binds method to specific instance
    getInfo := processor.GetInfo // No parentheses - getting method value
    fmt.Printf("Method value result: %s\n", getInfo())

    // Method expression - requires passing receiver explicitly
    var getInfoExpr func(TaskProcessor) string = TaskProcessor.GetInfo
    fmt.Printf("Method expression result: %s\n", getInfoExpr(processor))

    // Pointer method value
    activate := processor.Activate // Binds to processor instance
    activate()                     // Activates the original processor
    fmt.Printf("After activation: %s\n", processor.GetInfo())

    // Pointer method expression
    var activateExpr func(*TaskProcessor) = (*TaskProcessor).Activate
    newProcessor := TaskProcessor{Name: "NewProcessor", Priority: 2}
    activateExpr(&newProcessor) // Must pass pointer explicitly
}

func main() {
    // Create processors
    processors := []TaskProcessor{
        {Name: "FastProcessor", Priority: 1, Active: false},
        {Name: "SlowProcessor", Priority: 2, Active: false},
        {Name: "BackupProcessor", Priority: 3, Active: false},
    }

    // Activate processors
    for i := range processors {
        processors[i].Activate()
    }

    // Create pipeline
    pipeline := AutomationPipeline{
        Name:       "MainPipeline",
        Processors: processors,
    }

    // Configure strategy using method value
    pipeline.Strategy = pipeline.GetProcessorStrategy("FastProcessor")

    // Execute tasks
    tasks := []string{"TASK-001", "TASK-002", "TASK-003"}
    fmt.Println("=== Executing with FastProcessor strategy ===")
    pipeline.ExecuteTasks(tasks)

    // Change strategy
    pipeline.Strategy = pipeline.GetProcessorStrategy("SlowProcessor")
    fmt.Println("\n=== Executing with SlowProcessor strategy ===")
    pipeline.ExecuteTasks(tasks)

    // Try non-existent processor
    pipeline.Strategy = pipeline.GetProcessorStrategy("NonExistent")
    fmt.Println("\n=== Executing with non-existent processor ===")
    pipeline.ExecuteTasks([]string{"TASK-004"})

    // Demonstrate specialized processor (no behavior inheritance)
    specialized := SpecializedProcessor{
        Name:     "SpecialProcessor",
        Priority: 1,
        Active:   true,
    }

    fmt.Printf("\n=== Specialized Processor ===\n")
    fmt.Printf("%s\n", specialized.GetSpecialInfo())
    // specialized.GetInfo() // This would NOT compile - no inherited behavior

    // But we can convert to access underlying type methods
    base := TaskProcessor(specialized)
    fmt.Printf("Converted to base: %s\n", base.GetInfo())

    // Demonstrate method types
    demonstrateMethodTypes()
}
```

**Prerequisites**: Session 16

---

### Session 18: Interfaces Part 1: Polymorphism and Design

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand polymorphism and its role in decoupling
- Master interface declaration and implementation concepts
- Learn interface design principles (behavior over nouns)
- Understand interface values and their internal structure
- Apply polymorphic design to automation systems
- Design efficient APIs that minimize allocations

**Videos Covered**:

- 4.2 Interfaces Part 1 (Polymorphism) (0:45:20)

**Key Concepts**:

- Polymorphism: code behavior changes based on concrete data
- Interface types are not real - they define method sets (contracts)
- Interfaces should define behavior (verbs) not things (nouns)
- Interface values store concrete data using two-pointer structure
- API design: minimize allocations and prevent misuse
- Concrete data drives polymorphism through behavior
- Static analysis verifies interface satisfaction at compile time

**Hands-on Exercise**:

```go
// Polymorphic automation system with interface design
package main

import (
    "fmt"
    "log"
    "strings"
    "time"
)

// DataProcessor interface defines behavior for processing data
// Notice: focuses on behavior (verb) not nouns
type DataProcessor interface {
    Process(data []byte) ([]byte, error)
}

// LogWriter interface for different logging destinations
type LogWriter interface {
    WriteLog(level string, message string) error
}

// FileProcessor implements DataProcessor for file-based processing
type FileProcessor struct {
    Name      string
    Extension string
}

// Value receiver - implements DataProcessor interface
func (fp FileProcessor) Process(data []byte) ([]byte, error) {
    // Simulate file processing
    processed := fmt.Sprintf("[FILE:%s] %s", fp.Extension, string(data))
    log.Printf("FileProcessor %s processed %d bytes", fp.Name, len(data))
    return []byte(processed), nil
}

// NetworkProcessor implements DataProcessor for network-based processing
type NetworkProcessor struct {
    Endpoint string
    Timeout  time.Duration
}

// Value receiver - implements DataProcessor interface
func (np NetworkProcessor) Process(data []byte) ([]byte, error) {
    // Simulate network processing
    processed := fmt.Sprintf("[NETWORK:%s] %s", np.Endpoint, string(data))
    log.Printf("NetworkProcessor sent %d bytes to %s", len(data), np.Endpoint)

    // Simulate network delay
    time.Sleep(np.Timeout)
    return []byte(processed), nil
}

// ConsoleLogger implements LogWriter for console output
type ConsoleLogger struct {
    Prefix string
}

// Pointer receiver - implements LogWriter interface
func (cl *ConsoleLogger) WriteLog(level string, message string) error {
    timestamp := time.Now().Format("15:04:05")
    fmt.Printf("[%s] %s %s: %s\n", timestamp, cl.Prefix, level, message)
    return nil
}

// FileLogger implements LogWriter for file output
type FileLogger struct {
    Filename string
    MaxSize  int
}

// Pointer receiver - implements LogWriter interface
func (fl *FileLogger) WriteLog(level string, message string) error {
    // Simulate file writing
    entry := fmt.Sprintf("[%s] %s: %s", time.Now().Format("2006-01-02 15:04:05"), level, message)
    log.Printf("Writing to %s: %s", fl.Filename, entry)
    return nil
}

// Polymorphic function - works with any DataProcessor implementation
func ProcessAutomationData(processor DataProcessor, datasets [][]byte) [][]byte {
    var results [][]byte

    for i, data := range datasets {
        log.Printf("Processing dataset %d with %T", i+1, processor)

        result, err := processor.Process(data)
        if err != nil {
            log.Printf("Error processing dataset %d: %v", i+1, err)
            continue
        }

        results = append(results, result)
    }

    return results
}

// Polymorphic function - works with any LogWriter implementation
func LogResults(logger LogWriter, results [][]byte) {
    for i, result := range results {
        message := fmt.Sprintf("Result %d: %s", i+1, string(result))
        logger.WriteLog("INFO", message)
    }
}

// AutomationPipeline demonstrates polymorphic design
type AutomationPipeline struct {
    Name      string
    Processor DataProcessor // Interface field - can hold any implementation
    Logger    LogWriter     // Interface field - can hold any implementation
}

// Execute pipeline using polymorphic behavior
func (ap *AutomationPipeline) Execute(datasets [][]byte) {
    ap.Logger.WriteLog("INFO", fmt.Sprintf("Starting pipeline: %s", ap.Name))

    // Process data using whatever processor is configured
    results := ProcessAutomationData(ap.Processor, datasets)

    // Log results using whatever logger is configured
    LogResults(ap.Logger, results)

    ap.Logger.WriteLog("INFO", fmt.Sprintf("Pipeline %s completed", ap.Name))
}

// Interface composition - combining multiple interfaces
type ProcessorWithLogging interface {
    DataProcessor
    LogWriter
}

// AdvancedProcessor implements both interfaces
type AdvancedProcessor struct {
    Name    string
    LogFile string
}

func (ap AdvancedProcessor) Process(data []byte) ([]byte, error) {
    processed := fmt.Sprintf("[ADVANCED:%s] %s", ap.Name, strings.ToUpper(string(data)))
    return []byte(processed), nil
}

func (ap AdvancedProcessor) WriteLog(level string, message string) error {
    entry := fmt.Sprintf("[%s] ADVANCED %s: %s", time.Now().Format("15:04:05"), level, message)
    fmt.Println(entry)
    return nil
}

func main() {
    // Create sample datasets
    datasets := [][]byte{
        []byte("automation task 1"),
        []byte("automation task 2"),
        []byte("automation task 3"),
    }

    fmt.Println("=== Polymorphic Data Processing ===")

    // Create different processor implementations
    fileProcessor := FileProcessor{
        Name:      "CSVProcessor",
        Extension: "csv",
    }

    networkProcessor := NetworkProcessor{
        Endpoint: "api.example.com",
        Timeout:  50 * time.Millisecond,
    }

    // Demonstrate polymorphism - same function, different behavior
    fmt.Println("\n--- Using FileProcessor ---")
    fileResults := ProcessAutomationData(fileProcessor, datasets)

    fmt.Println("\n--- Using NetworkProcessor ---")
    networkResults := ProcessAutomationData(networkProcessor, datasets)

    fmt.Println("\n=== Polymorphic Logging ===")

    // Create different logger implementations
    consoleLogger := &ConsoleLogger{Prefix: "AUTOMATION"}
    fileLogger := &FileLogger{Filename: "automation.log", MaxSize: 1024}

    // Demonstrate polymorphic logging
    fmt.Println("\n--- Console Logging ---")
    LogResults(consoleLogger, fileResults)

    fmt.Println("\n--- File Logging ---")
    LogResults(fileLogger, networkResults)

    fmt.Println("\n=== Pipeline with Polymorphic Components ===")

    // Create pipelines with different configurations
    pipeline1 := AutomationPipeline{
        Name:      "FilePipeline",
        Processor: fileProcessor,
        Logger:    consoleLogger,
    }

    pipeline2 := AutomationPipeline{
        Name:      "NetworkPipeline",
        Processor: networkProcessor,
        Logger:    fileLogger,
    }

    // Execute pipelines - same code, different behavior
    fmt.Println("\n--- Pipeline 1 Execution ---")
    pipeline1.Execute(datasets[:2])

    fmt.Println("\n--- Pipeline 2 Execution ---")
    pipeline2.Execute(datasets[1:])

    fmt.Println("\n=== Interface Composition ===")

    // Advanced processor implements multiple interfaces
    advanced := AdvancedProcessor{
        Name:    "MultiProcessor",
        LogFile: "advanced.log",
    }

    // Can be used as DataProcessor
    advancedResults := ProcessAutomationData(advanced, [][]byte{[]byte("test data")})

    // Can be used as LogWriter
    LogResults(advanced, advancedResults)

    // Can be used as combined interface
    var combined ProcessorWithLogging = advanced
    combinedResults, _ := combined.Process([]byte("combined processing"))
    combined.WriteLog("INFO", fmt.Sprintf("Combined result: %s", string(combinedResults)))
}
```

**Prerequisites**: Session 17

---

### Session 19: Interfaces Part 2: Method Sets and Storage

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand method sets and interface satisfaction rules
- Master interface value storage mechanics (two-word structure)
- Learn address-of-value vs value semantics with interfaces
- Understand interface conversion and type assertions
- Apply interface storage patterns in automation systems

**Videos Covered**:

- 4.2 Interfaces Part 2 (Method Sets and Address of Value) (0:22:15)
- 4.2 Interfaces Part 3 (Storage by Value) (0:18:45)

**Key Concepts**:

- Method sets determine interface satisfaction
- Interface values: two-word structure (type info + data pointer)
- Value vs pointer method sets and interface compatibility
- Storage by value: interfaces store copies of concrete data
- Type assertions and interface conversions
- Performance implications of interface storage

**Hands-on Exercise**:

```go
// Interface method sets and storage in automation monitoring
package main

import (
    "fmt"
    "log"
    "time"
)

// Monitor interface with value receiver method
type Monitor interface {
    Status() string
}

// AlertSender interface with pointer receiver method
type AlertSender interface {
    SendAlert(message string) error
}

// Combined interface
type MonitorWithAlerts interface {
    Monitor
    AlertSender
}

// ServiceMonitor implements monitoring functionality
type ServiceMonitor struct {
    ServiceName string
    LastCheck   time.Time
    IsHealthy   bool
}

// Value receiver - part of value method set
func (sm ServiceMonitor) Status() string {
    status := "unhealthy"
    if sm.IsHealthy {
        status = "healthy"
    }
    return fmt.Sprintf("Service %s is %s (last check: %v)",
        sm.ServiceName, status, sm.LastCheck.Format("15:04:05"))
}

// Pointer receiver - part of pointer method set only
func (sm *ServiceMonitor) SendAlert(message string) error {
    log.Printf("ALERT for %s: %s", sm.ServiceName, message)
    return nil
}

// Pointer receiver - updates state
func (sm *ServiceMonitor) UpdateHealth(healthy bool) {
    sm.IsHealthy = healthy
    sm.LastCheck = time.Now()
}

// Function demonstrating method set rules
func demonstrateMethodSets() {
    fmt.Println("=== Method Set Demonstration ===")

    monitor := ServiceMonitor{
        ServiceName: "WebAPI",
        LastCheck:   time.Now(),
        IsHealthy:   true,
    }

    // Value can satisfy Monitor interface (value receiver method)
    var m Monitor = monitor
    fmt.Printf("Monitor status: %s\n", m.Status())

    // Value CANNOT satisfy AlertSender interface (pointer receiver method)
    // var a AlertSender = monitor // This would NOT compile

    // Pointer can satisfy both interfaces
    var ma MonitorWithAlerts = &monitor
    fmt.Printf("Combined status: %s\n", ma.Status())
    ma.SendAlert("Test alert")

    // Demonstrate interface storage
    fmt.Println("\n=== Interface Storage ===")

    // Interface stores a COPY of the value
    var valueMonitor Monitor = monitor

    // Modify original
    monitor.IsHealthy = false
    monitor.LastCheck = time.Now()

    fmt.Printf("Original: %s\n", monitor.Status())
    fmt.Printf("Interface copy: %s\n", valueMonitor.Status()) // Still shows old state!

    // Interface with pointer stores reference
    var pointerMonitor Monitor = &monitor
    fmt.Printf("Interface pointer: %s\n", pointerMonitor.Status()) // Shows current state
}

// Type assertion and conversion examples
func demonstrateTypeAssertions() {
    fmt.Println("\n=== Type Assertions ===")

    monitor := &ServiceMonitor{
        ServiceName: "DatabaseAPI",
        LastCheck:   time.Now(),
        IsHealthy:   true,
    }

    // Store in interface
    var m Monitor = monitor

    // Type assertion - extract concrete type
    if sm, ok := m.(*ServiceMonitor); ok {
        fmt.Printf("Asserted type: %T\n", sm)
        sm.UpdateHealth(false)
        fmt.Printf("After update: %s\n", sm.Status())
    }

    // Type switch
    switch v := m.(type) {
    case *ServiceMonitor:
        fmt.Printf("Type switch found ServiceMonitor: %s\n", v.ServiceName)
    default:
        fmt.Printf("Unknown type: %T\n", v)
    }
}

// AutomationSystem demonstrates interface storage patterns
type AutomationSystem struct {
    monitors []Monitor
    alerters []AlertSender
}

func (as *AutomationSystem) AddMonitor(m Monitor) {
    as.monitors = append(as.monitors, m)
}

func (as *AutomationSystem) AddAlerter(a AlertSender) {
    as.alerters = append(as.alerters, a)
}

func (as *AutomationSystem) CheckAll() {
    fmt.Println("\n=== System Check ===")
    for i, monitor := range as.monitors {
        status := monitor.Status()
        fmt.Printf("Monitor %d: %s\n", i+1, status)

        // If monitor is unhealthy, send alerts
        if !isHealthy(status) {
            for _, alerter := range as.alerters {
                alerter.SendAlert(fmt.Sprintf("Monitor %d is unhealthy", i+1))
            }
        }
    }
}

func isHealthy(status string) bool {
    return !fmt.Sprintf("%s", status)[len(status)-9:] == "unhealthy"
}

func main() {
    // Demonstrate method sets
    demonstrateMethodSets()

    // Demonstrate type assertions
    demonstrateTypeAssertions()

    // Create automation system
    system := &AutomationSystem{}

    // Create monitors
    webMonitor := ServiceMonitor{
        ServiceName: "WebService",
        LastCheck:   time.Now(),
        IsHealthy:   true,
    }

    dbMonitor := &ServiceMonitor{
        ServiceName: "Database",
        LastCheck:   time.Now(),
        IsHealthy:   false,
    }

    apiMonitor := &ServiceMonitor{
        ServiceName: "APIGateway",
        LastCheck:   time.Now(),
        IsHealthy:   true,
    }

    // Add monitors (interface storage)
    system.AddMonitor(webMonitor)  // Stores copy
    system.AddMonitor(dbMonitor)   // Stores pointer
    system.AddMonitor(apiMonitor)  // Stores pointer

    // Add alerters
    system.AddAlerter(dbMonitor)   // Can be alerter (pointer receiver)
    system.AddAlerter(apiMonitor)  // Can be alerter (pointer receiver)

    // Check system
    system.CheckAll()

    // Modify original monitors
    fmt.Println("\n=== After Modifications ===")
    webMonitor.IsHealthy = false  // Won't affect interface copy
    dbMonitor.UpdateHealth(true)  // Will affect interface pointer

    system.CheckAll()
}
```

**Prerequisites**: Session 18

---

### Session 20: Embedding, Exporting, and Composition Patterns

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master embedding for composition and method promotion
- Understand exporting rules and package visibility
- Learn composition patterns vs inheritance
- Apply embedding in automation system architecture
- Design clean APIs with proper encapsulation

**Videos Covered**:

- 4.3 Embedding (0:25:30)
- 4.4 Exporting (0:18:45)

**Key Concepts**:

- Embedding: composition through anonymous fields
- Method promotion: embedded type methods become available
- Inner type promotion and method set expansion
- Exporting: uppercase names are public, lowercase are private
- Package-level encapsulation and API design
- Composition over inheritance patterns

**Hands-on Exercise**:

```go
// Embedding and composition in automation framework
package main

import (
    "fmt"
    "log"
    "time"
)

// Base functionality through embedding

// Logger provides basic logging capability
type Logger struct {
    prefix string
    level  string
}

// Exported method - available outside package
func (l *Logger) Log(message string) {
    timestamp := time.Now().Format("15:04:05")
    fmt.Printf("[%s] %s %s: %s\n", timestamp, l.prefix, l.level, message)
}

// unexported method - internal use only
func (l *Logger) formatMessage(message string) string {
    return fmt.Sprintf("%s: %s", l.prefix, message)
}

// SetLevel is exported - can be called from outside
func (l *Logger) SetLevel(level string) {
    l.level = level
}

// Metrics provides performance tracking
type Metrics struct {
    startTime time.Time
    counter   int64
}

// Exported methods
func (m *Metrics) Start() {
    m.startTime = time.Now()
    m.counter = 0
}

func (m *Metrics) Increment() {
    m.counter++
}

func (m *Metrics) GetStats() (time.Duration, int64) {
    return time.Since(m.startTime), m.counter
}

// AutomationTask embeds Logger and Metrics
type AutomationTask struct {
    // Embedded types - anonymous fields
    *Logger  // Pointer embedding
    Metrics  // Value embedding

    // Own fields
    ID          string
    Description string
    Status      string
}

// Constructor function (exported)
func NewAutomationTask(id, description string) *AutomationTask {
    return &AutomationTask{
        Logger: &Logger{
            prefix: fmt.Sprintf("TASK-%s", id),
            level:  "INFO",
        },
        Metrics:     Metrics{},
        ID:          id,
        Description: description,
        Status:      "created",
    }
}

// Own methods
func (at *AutomationTask) Execute() error {
    // Use embedded Logger methods directly
    at.Log("Starting task execution")
    at.Start() // Use embedded Metrics method

    at.Status = "running"
    at.Log(fmt.Sprintf("Executing: %s", at.Description))

    // Simulate work
    for i := 0; i < 5; i++ {
        time.Sleep(100 * time.Millisecond)
        at.Increment() // Use embedded Metrics method
        at.Log(fmt.Sprintf("Progress: %d/5", i+1))
    }

    at.Status = "completed"
    duration, count := at.GetStats() // Use embedded Metrics method
    at.Log(fmt.Sprintf("Task completed in %v with %d operations", duration, count))

    return nil
}

// Method that shadows embedded method (method overriding)
func (at *AutomationTask) Log(message string) {
    // Call embedded Logger's Log method explicitly
    at.Logger.Log(fmt.Sprintf("[%s] %s", at.Status, message))
}

// Advanced embedding with interfaces

// Processor interface
type Processor interface {
    Process(data string) (string, error)
}

// Validator interface
type Validator interface {
    Validate(data string) error
}

// BasicProcessor provides basic processing
type BasicProcessor struct {
    name string
}

func (bp *BasicProcessor) Process(data string) (string, error) {
    return fmt.Sprintf("processed_%s_%s", bp.name, data), nil
}

// BasicValidator provides basic validation
type BasicValidator struct {
    rules []string
}

func (bv *BasicValidator) Validate(data string) error {
    if len(data) == 0 {
        return fmt.Errorf("data cannot be empty")
    }
    return nil
}

// AdvancedAutomationService embeds multiple types
type AdvancedAutomationService struct {
    // Embed interfaces - composition through interfaces
    Processor
    Validator

    // Embed concrete types
    *Logger

    // Own fields
    ServiceName string
    isActive    bool // unexported field
}

// Constructor
func NewAdvancedService(name string, processor Processor, validator Validator) *AdvancedAutomationService {
    return &AdvancedAutomationService{
        Processor:   processor,
        Validator:   validator,
        Logger:      &Logger{prefix: name, level: "INFO"},
        ServiceName: name,
        isActive:    true,
    }
}

// Own methods
func (aas *AdvancedAutomationService) ProcessData(data string) (string, error) {
    aas.Log("Starting data processing")

    // Use embedded Validator
    if err := aas.Validate(data); err != nil {
        aas.Log(fmt.Sprintf("Validation failed: %v", err))
        return "", err
    }

    // Use embedded Processor
    result, err := aas.Process(data)
    if err != nil {
        aas.Log(fmt.Sprintf("Processing failed: %v", err))
        return "", err
    }

    aas.Log("Data processing completed successfully")
    return result, nil
}

// Exported method to check if service is active
func (aas *AdvancedAutomationService) IsActive() bool {
    return aas.isActive
}

// unexported method - internal use
func (aas *AdvancedAutomationService) setActive(active bool) {
    aas.isActive = active
}

func main() {
    fmt.Println("=== Basic Embedding Example ===")

    // Create automation task
    task := NewAutomationTask("001", "Process daily reports")

    // Embedded methods are promoted and directly accessible
    task.SetLevel("DEBUG") // From embedded Logger

    // Execute task
    if err := task.Execute(); err != nil {
        log.Printf("Task execution failed: %v", err)
    }

    fmt.Println("\n=== Advanced Embedding with Interfaces ===")

    // Create components
    processor := &BasicProcessor{name: "DataProcessor"}
    validator := &BasicValidator{rules: []string{"not_empty"}}

    // Create service with embedded interfaces
    service := NewAdvancedService("AdvancedService", processor, validator)

    // Test data processing
    testData := []string{"sample_data", "another_sample", ""}

    for _, data := range testData {
        fmt.Printf("\nProcessing: '%s'\n", data)
        result, err := service.ProcessData(data)
        if err != nil {
            fmt.Printf("Error: %v\n", err)
        } else {
            fmt.Printf("Result: %s\n", result)
        }
    }

    fmt.Println("\n=== Method Promotion and Shadowing ===")

    // Direct access to embedded methods
    service.Log("Direct access to embedded Logger")

    // Access embedded interface methods directly
    directResult, _ := service.Process("direct_call")
    fmt.Printf("Direct processor call result: %s\n", directResult)

    // Check service status
    fmt.Printf("Service active: %t\n", service.IsActive())

    // Demonstrate composition flexibility
    fmt.Println("\n=== Composition Flexibility ===")

    // Can replace embedded components
    newProcessor := &BasicProcessor{name: "NewProcessor"}
    service.Processor = newProcessor

    result, _ := service.ProcessData("flexibility_test")
    fmt.Printf("With new processor: %s\n", result)
}
```

**Prerequisites**: Session 19

---

### Session 21: Grouping Types and Decoupling Strategies

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand Go's approach to grouping: behavior over configuration
- Learn why embedding doesn't create inheritance relationships
- Master interface-based grouping and composition patterns
- Apply decoupling strategies in automation system design
- Design flexible, extensible automation architectures

**Videos Covered**:

- 5.1 Grouping Types (0:28:45)
- 5.2 Decoupling Part 1 (0:22:30)
- 5.2 Decoupling Part 2 (0:18:15)
- 5.2 Decoupling Part 3 (0:25:20)

**Key Concepts**:

- Convention over configuration: group by behavior, not identity
- No inheritance in Go: embedding ≠ subtyping
- Interface-based grouping enables diversity and flexibility
- Decoupling through behavior contracts
- Composition patterns for extensible systems
- Design from concrete to abstract

**Hands-on Exercise**:

```go
// Grouping and decoupling in automation system architecture
package main

import (
    "fmt"
    "log"
    "time"
)

// WRONG APPROACH: Grouping by what things ARE (configuration)
// This demonstrates the common mistake from OOP backgrounds

type AutomationComponent struct {
    Name        string
    Version     string
    IsActive    bool
    CreatedAt   time.Time
}

func (ac AutomationComponent) GetInfo() string {
    return fmt.Sprintf("%s v%s (Active: %t)", ac.Name, ac.Version, ac.IsActive)
}

// Embedding doesn't create inheritance - these are different types!
type DataProcessor struct {
    AutomationComponent // Embedding
    ProcessingRate      int
}

func (dp DataProcessor) ProcessData(data string) string {
    return fmt.Sprintf("Processed by %s: %s", dp.Name, data)
}

type AlertManager struct {
    AutomationComponent // Embedding
    AlertThreshold      float64
}

func (am AlertManager) SendAlert(message string) {
    fmt.Printf("ALERT from %s: %s\n", am.Name, message)
}

// This WILL NOT WORK - cannot group by embedded type
// var components []AutomationComponent = []AutomationComponent{
//     DataProcessor{...}, // Compile error!
//     AlertManager{...},  // Compile error!
// }

// CORRECT APPROACH: Grouping by what things DO (behavior)

// Define behaviors (interfaces) for grouping
type Processor interface {
    Process(data string) (string, error)
}

type Alerter interface {
    Alert(message string) error
}

type StatusReporter interface {
    Status() string
}

type Configurable interface {
    Configure(config map[string]interface{}) error
}

// Concrete implementations focused on behavior
type FileProcessor struct {
    name      string
    directory string
    isActive  bool
}

func (fp *FileProcessor) Process(data string) (string, error) {
    if !fp.isActive {
        return "", fmt.Errorf("processor %s is not active", fp.name)
    }
    result := fmt.Sprintf("File processed: %s -> %s/%s.processed", data, fp.directory, data)
    return result, nil
}

func (fp *FileProcessor) Status() string {
    status := "inactive"
    if fp.isActive {
        status = "active"
    }
    return fmt.Sprintf("FileProcessor %s: %s", fp.name, status)
}

func (fp *FileProcessor) Configure(config map[string]interface{}) error {
    if dir, ok := config["directory"].(string); ok {
        fp.directory = dir
    }
    if active, ok := config["active"].(bool); ok {
        fp.isActive = active
    }
    return nil
}

type NetworkProcessor struct {
    name     string
    endpoint string
    timeout  time.Duration
    isActive bool
}

func (np *NetworkProcessor) Process(data string) (string, error) {
    if !np.isActive {
        return "", fmt.Errorf("processor %s is not active", np.name)
    }
    // Simulate network processing
    time.Sleep(np.timeout)
    result := fmt.Sprintf("Network processed: %s via %s", data, np.endpoint)
    return result, nil
}

func (np *NetworkProcessor) Status() string {
    status := "inactive"
    if np.isActive {
        status = "active"
    }
    return fmt.Sprintf("NetworkProcessor %s: %s (endpoint: %s)", np.name, status, np.endpoint)
}

func (np *NetworkProcessor) Configure(config map[string]interface{}) error {
    if endpoint, ok := config["endpoint"].(string); ok {
        np.endpoint = endpoint
    }
    if timeout, ok := config["timeout"].(time.Duration); ok {
        np.timeout = timeout
    }
    if active, ok := config["active"].(bool); ok {
        np.isActive = active
    }
    return nil
}

type EmailAlerter struct {
    name      string
    smtpHost  string
    recipients []string
    isActive  bool
}

func (ea *EmailAlerter) Alert(message string) error {
    if !ea.isActive {
        return fmt.Errorf("alerter %s is not active", ea.name)
    }
    fmt.Printf("EMAIL ALERT from %s via %s: %s\n", ea.name, ea.smtpHost, message)
    fmt.Printf("Recipients: %v\n", ea.recipients)
    return nil
}

func (ea *EmailAlerter) Status() string {
    status := "inactive"
    if ea.isActive {
        status = "active"
    }
    return fmt.Sprintf("EmailAlerter %s: %s", ea.name, status)
}

func (ea *EmailAlerter) Configure(config map[string]interface{}) error {
    if host, ok := config["smtp_host"].(string); ok {
        ea.smtpHost = host
    }
    if recipients, ok := config["recipients"].([]string); ok {
        ea.recipients = recipients
    }
    if active, ok := config["active"].(bool); ok {
        ea.isActive = active
    }
    return nil
}

type SlackAlerter struct {
    name      string
    webhook   string
    channel   string
    isActive  bool
}

func (sa *SlackAlerter) Alert(message string) error {
    if !sa.isActive {
        return fmt.Errorf("alerter %s is not active", sa.name)
    }
    fmt.Printf("SLACK ALERT from %s to #%s: %s\n", sa.name, sa.channel, message)
    return nil
}

func (sa *SlackAlerter) Status() string {
    status := "inactive"
    if sa.isActive {
        status = "active"
    }
    return fmt.Sprintf("SlackAlerter %s: %s (channel: #%s)", sa.name, status, sa.channel)
}

func (sa *SlackAlerter) Configure(config map[string]interface{}) error {
    if webhook, ok := config["webhook"].(string); ok {
        sa.webhook = webhook
    }
    if channel, ok := config["channel"].(string); ok {
        sa.channel = channel
    }
    if active, ok := config["active"].(bool); ok {
        sa.isActive = active
    }
    return nil
}

// AutomationSystem groups components by behavior, not identity
type AutomationSystem struct {
    name        string
    processors  []Processor      // Group by behavior
    alerters    []Alerter        // Group by behavior
    reporters   []StatusReporter // Group by behavior
    configurables []Configurable // Group by behavior
}

func NewAutomationSystem(name string) *AutomationSystem {
    return &AutomationSystem{
        name:          name,
        processors:    make([]Processor, 0),
        alerters:      make([]Alerter, 0),
        reporters:     make([]StatusReporter, 0),
        configurables: make([]Configurable, 0),
    }
}

func (as *AutomationSystem) AddProcessor(p Processor) {
    as.processors = append(as.processors, p)
    if reporter, ok := p.(StatusReporter); ok {
        as.reporters = append(as.reporters, reporter)
    }
    if configurable, ok := p.(Configurable); ok {
        as.configurables = append(as.configurables, configurable)
    }
}

func (as *AutomationSystem) AddAlerter(a Alerter) {
    as.alerters = append(as.alerters, a)
    if reporter, ok := a.(StatusReporter); ok {
        as.reporters = append(as.reporters, reporter)
    }
    if configurable, ok := a.(Configurable); ok {
        as.configurables = append(as.configurables, configurable)
    }
}

func (as *AutomationSystem) ProcessData(data []string) {
    fmt.Printf("\n=== Processing Data with %s ===\n", as.name)

    for _, item := range data {
        for i, processor := range as.processors {
            result, err := processor.Process(item)
            if err != nil {
                // Alert on error
                errorMsg := fmt.Sprintf("Processor %d failed: %v", i, err)
                for _, alerter := range as.alerters {
                    alerter.Alert(errorMsg)
                }
            } else {
                fmt.Printf("Result: %s\n", result)
            }
        }
    }
}

func (as *AutomationSystem) SystemStatus() {
    fmt.Printf("\n=== System Status: %s ===\n", as.name)
    for _, reporter := range as.reporters {
        fmt.Printf("- %s\n", reporter.Status())
    }
}

func (as *AutomationSystem) ConfigureAll(config map[string]interface{}) {
    fmt.Printf("\n=== Configuring %s ===\n", as.name)
    for _, configurable := range as.configurables {
        if err := configurable.Configure(config); err != nil {
            log.Printf("Configuration error: %v", err)
        }
    }
}

func main() {
    // Create automation system
    system := NewAutomationSystem("MainAutomationSystem")

    // Create diverse components - grouped by behavior, not identity
    fileProc := &FileProcessor{name: "CSVProcessor", directory: "/data", isActive: true}
    netProc := &NetworkProcessor{name: "APIProcessor", endpoint: "api.example.com", timeout: 100 * time.Millisecond, isActive: true}

    emailAlert := &EmailAlerter{name: "CriticalAlerts", smtpHost: "smtp.company.com", recipients: []string{"<EMAIL>"}, isActive: true}
    slackAlert := &SlackAlerter{name: "TeamAlerts", webhook: "https://hooks.slack.com/...", channel: "automation", isActive: true}

    // Add components - they're grouped by what they DO, not what they ARE
    system.AddProcessor(fileProc)
    system.AddProcessor(netProc)
    system.AddAlerter(emailAlert)
    system.AddAlerter(slackAlert)

    // System status - all components that can report status
    system.SystemStatus()

    // Process data - all processors work together
    testData := []string{"data1.csv", "data2.json", "data3.xml"}
    system.ProcessData(testData)

    // Configure all configurable components
    config := map[string]interface{}{
        "active":     true,
        "directory":  "/new-data",
        "endpoint":   "new-api.example.com",
        "smtp_host":  "new-smtp.company.com",
        "channel":    "new-automation",
    }
    system.ConfigureAll(config)

    // Status after configuration
    system.SystemStatus()

    // Demonstrate flexibility - add new component types
    fmt.Println("\n=== Adding New Component Types ===")

    // New processor type that also alerts
    hybridProc := &HybridProcessor{name: "HybridProcessor", isActive: true}
    system.AddProcessor(hybridProc)
    system.AddAlerter(hybridProc) // Same component, different behavior grouping

    system.SystemStatus()
}

// HybridProcessor demonstrates multiple interface implementation
type HybridProcessor struct {
    name     string
    isActive bool
}

func (hp *HybridProcessor) Process(data string) (string, error) {
    if !hp.isActive {
        return "", fmt.Errorf("hybrid processor is not active")
    }
    return fmt.Sprintf("Hybrid processed: %s", data), nil
}

func (hp *HybridProcessor) Alert(message string) error {
    if !hp.isActive {
        return fmt.Errorf("hybrid alerter is not active")
    }
    fmt.Printf("HYBRID ALERT from %s: %s\n", hp.name, message)
    return nil
}

func (hp *HybridProcessor) Status() string {
    status := "inactive"
    if hp.isActive {
        status = "active"
    }
    return fmt.Sprintf("HybridProcessor %s: %s", hp.name, status)
}

func (hp *HybridProcessor) Configure(config map[string]interface{}) error {
    if active, ok := config["active"].(bool); ok {
        hp.isActive = active
    }
    return nil
}
```

**Prerequisites**: Session 20

---

### Session 22: Interface Conversions, Assertions, and Design Guidelines

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master interface conversions and type assertions
- Understand interface pollution and when to avoid it
- Learn interface design guidelines and best practices
- Apply clean interface design in automation systems
- Recognize and prevent interface anti-patterns

**Videos Covered**:

- 5.3 Conversion and Assertions (0:18:30)
- 5.4 Interface Pollution (0:22:15)
- 5.6 Design Guidelines (0:25:45)

**Key Concepts**:

- Type assertions: extracting concrete types from interfaces
- Interface conversions: moving between interface types
- Interface pollution: over-abstraction and premature interfaces
- Design guidelines: start concrete, move to abstract when needed
- Interface segregation: small, focused interfaces
- Avoid interfaces for the sake of interfaces

**Hands-on Exercise**:

```go
// Interface design patterns and anti-patterns in automation
package main

import (
    "fmt"
    "log"
    "time"
)

// GOOD: Small, focused interfaces
type DataReader interface {
    Read() ([]byte, error)
}

type DataWriter interface {
    Write(data []byte) error
}

type DataProcessor interface {
    Process(data []byte) ([]byte, error)
}

// GOOD: Interface composition when needed
type DataHandler interface {
    DataReader
    DataWriter
    DataProcessor
}

// BAD: Interface pollution - too many methods
type MegaInterface interface {
    Read() ([]byte, error)
    Write(data []byte) error
    Process(data []byte) ([]byte, error)
    Configure(config map[string]string) error
    Start() error
    Stop() error
    Status() string
    Metrics() map[string]int64
    Reset() error
    Validate() error
    // ... many more methods
}

// Concrete implementations
type FileHandler struct {
    filename string
    data     []byte
}

func (fh *FileHandler) Read() ([]byte, error) {
    // Simulate file reading
    fh.data = []byte(fmt.Sprintf("data from %s", fh.filename))
    return fh.data, nil
}

func (fh *FileHandler) Write(data []byte) error {
    fmt.Printf("Writing to %s: %s\n", fh.filename, string(data))
    return nil
}

func (fh *FileHandler) Process(data []byte) ([]byte, error) {
    processed := fmt.Sprintf("processed_%s", string(data))
    return []byte(processed), nil
}

// Type assertion and conversion examples
func demonstrateAssertions() {
    fmt.Println("=== Type Assertions and Conversions ===")

    var handler DataHandler = &FileHandler{filename: "test.txt"}

    // Type assertion - extract concrete type
    if fileHandler, ok := handler.(*FileHandler); ok {
        fmt.Printf("Successfully asserted to *FileHandler: %s\n", fileHandler.filename)
    }

    // Interface conversion - move to smaller interface
    var reader DataReader = handler
    data, _ := reader.Read()
    fmt.Printf("Read data: %s\n", string(data))

    // Type switch for multiple types
    switch v := handler.(type) {
    case *FileHandler:
        fmt.Printf("Type switch found FileHandler: %s\n", v.filename)
    default:
        fmt.Printf("Unknown type: %T\n", v)
    }
}

// GOOD: Interface design - start concrete, move to abstract
type AutomationTask struct {
    ID          string
    Description string
    Status      string
}

func (at *AutomationTask) Execute() error {
    at.Status = "running"
    fmt.Printf("Executing task %s: %s\n", at.ID, at.Description)
    time.Sleep(100 * time.Millisecond)
    at.Status = "completed"
    return nil
}

func (at *AutomationTask) GetStatus() string {
    return at.Status
}

// Only create interface when you need polymorphism
type Executable interface {
    Execute() error
}

type StatusReporter interface {
    GetStatus() string
}

// BAD: Premature interface creation
type TaskInterface interface {
    Execute() error
    GetStatus() string
    // Don't create this unless you have multiple implementations!
}

func main() {
    demonstrateAssertions()

    fmt.Println("\n=== Good Interface Design ===")

    // Start with concrete types
    task1 := &AutomationTask{ID: "T001", Description: "Process data"}
    task2 := &AutomationTask{ID: "T002", Description: "Send alerts"}

    // Create interface only when needed for grouping
    var executables []Executable = []Executable{task1, task2}

    for _, exec := range executables {
        exec.Execute()

        // Type assertion when you need concrete functionality
        if task, ok := exec.(*AutomationTask); ok {
            fmt.Printf("Task %s status: %s\n", task.ID, task.GetStatus())
        }
    }
}
```

**Prerequisites**: Session 21

---

### Session 23: Mocking and Testing Strategies

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand mocking through interfaces in Go
- Learn dependency injection patterns for testability
- Master test doubles and mock implementations
- Apply testing strategies to automation systems
- Design testable automation components

**Videos Covered**:

- 5.5 Mocking (0:28:30)

**Key Concepts**:

- Mocking through interfaces: dependency injection
- Test doubles: mocks, stubs, fakes
- Testable design: interfaces for external dependencies
- Dependency injection patterns in Go
- Testing automation workflows and integrations

**Hands-on Exercise**:

```go
// Mocking and testing strategies for automation systems
package main

import (
    "fmt"
    "testing"
    "time"
)

// External dependencies as interfaces for mocking
type DatabaseClient interface {
    Query(sql string) ([]map[string]interface{}, error)
    Execute(sql string) error
}

type EmailService interface {
    SendEmail(to, subject, body string) error
}

type FileSystem interface {
    ReadFile(filename string) ([]byte, error)
    WriteFile(filename string, data []byte) error
}

// Production implementations
type PostgresClient struct {
    connectionString string
}

func (pc *PostgresClient) Query(sql string) ([]map[string]interface{}, error) {
    // Real database query
    fmt.Printf("Executing query: %s\n", sql)
    return []map[string]interface{}{
        {"id": 1, "name": "test"},
    }, nil
}

func (pc *PostgresClient) Execute(sql string) error {
    fmt.Printf("Executing SQL: %s\n", sql)
    return nil
}

type SMTPEmailService struct {
    host string
    port int
}

func (ses *SMTPEmailService) SendEmail(to, subject, body string) error {
    fmt.Printf("Sending email to %s: %s\n", to, subject)
    return nil
}

type OSFileSystem struct{}

func (ofs *OSFileSystem) ReadFile(filename string) ([]byte, error) {
    return []byte(fmt.Sprintf("content of %s", filename)), nil
}

func (ofs *OSFileSystem) WriteFile(filename string, data []byte) error {
    fmt.Printf("Writing to %s: %s\n", filename, string(data))
    return nil
}

// Automation service with dependency injection
type AutomationService struct {
    db    DatabaseClient
    email EmailService
    fs    FileSystem
    name  string
}

func NewAutomationService(db DatabaseClient, email EmailService, fs FileSystem, name string) *AutomationService {
    return &AutomationService{
        db:    db,
        email: email,
        fs:    fs,
        name:  name,
    }
}

func (as *AutomationService) ProcessDailyReport() error {
    // Read configuration
    config, err := as.fs.ReadFile("config.json")
    if err != nil {
        return fmt.Errorf("failed to read config: %w", err)
    }

    // Query database
    results, err := as.db.Query("SELECT * FROM daily_metrics")
    if err != nil {
        return fmt.Errorf("failed to query database: %w", err)
    }

    // Generate report
    report := fmt.Sprintf("Daily Report - Config: %s, Results: %d records",
        string(config), len(results))

    // Save report
    if err := as.fs.WriteFile("daily_report.txt", []byte(report)); err != nil {
        return fmt.Errorf("failed to save report: %w", err)
    }

    // Send notification
    if err := as.email.SendEmail("<EMAIL>", "Daily Report Ready", report); err != nil {
        return fmt.Errorf("failed to send email: %w", err)
    }

    return nil
}

// Mock implementations for testing
type MockDatabaseClient struct {
    queryResults []map[string]interface{}
    queryError   error
    executeError error
    queryCalls   []string
    executeCalls []string
}

func (mdc *MockDatabaseClient) Query(sql string) ([]map[string]interface{}, error) {
    mdc.queryCalls = append(mdc.queryCalls, sql)
    return mdc.queryResults, mdc.queryError
}

func (mdc *MockDatabaseClient) Execute(sql string) error {
    mdc.executeCalls = append(mdc.executeCalls, sql)
    return mdc.executeError
}

type MockEmailService struct {
    sendError error
    sentEmails []EmailCall
}

type EmailCall struct {
    To      string
    Subject string
    Body    string
}

func (mes *MockEmailService) SendEmail(to, subject, body string) error {
    mes.sentEmails = append(mes.sentEmails, EmailCall{
        To:      to,
        Subject: subject,
        Body:    body,
    })
    return mes.sendError
}

type MockFileSystem struct {
    files     map[string][]byte
    readError error
    writeError error
    readCalls  []string
    writeCalls []string
}

func NewMockFileSystem() *MockFileSystem {
    return &MockFileSystem{
        files: make(map[string][]byte),
    }
}

func (mfs *MockFileSystem) ReadFile(filename string) ([]byte, error) {
    mfs.readCalls = append(mfs.readCalls, filename)
    if mfs.readError != nil {
        return nil, mfs.readError
    }
    if data, exists := mfs.files[filename]; exists {
        return data, nil
    }
    return []byte(fmt.Sprintf("mock content of %s", filename)), nil
}

func (mfs *MockFileSystem) WriteFile(filename string, data []byte) error {
    mfs.writeCalls = append(mfs.writeCalls, filename)
    if mfs.writeError != nil {
        return mfs.writeError
    }
    mfs.files[filename] = data
    return nil
}

// Test examples (would be in _test.go file)
func TestAutomationService_ProcessDailyReport(t *testing.T) {
    // Arrange - create mocks
    mockDB := &MockDatabaseClient{
        queryResults: []map[string]interface{}{
            {"id": 1, "metric": "cpu", "value": 75.5},
            {"id": 2, "metric": "memory", "value": 82.3},
        },
    }

    mockEmail := &MockEmailService{}
    mockFS := NewMockFileSystem()

    // Set up mock file system
    mockFS.files["config.json"] = []byte(`{"threshold": 80}`)

    service := NewAutomationService(mockDB, mockEmail, mockFS, "TestService")

    // Act
    err := service.ProcessDailyReport()

    // Assert
    if err != nil {
        t.Errorf("Expected no error, got %v", err)
    }

    // Verify database was called
    if len(mockDB.queryCalls) != 1 {
        t.Errorf("Expected 1 database query, got %d", len(mockDB.queryCalls))
    }

    // Verify email was sent
    if len(mockEmail.sentEmails) != 1 {
        t.Errorf("Expected 1 email, got %d", len(mockEmail.sentEmails))
    }

    // Verify file operations
    if len(mockFS.readCalls) != 1 || mockFS.readCalls[0] != "config.json" {
        t.Errorf("Expected config.json to be read")
    }

    if len(mockFS.writeCalls) != 1 || mockFS.writeCalls[0] != "daily_report.txt" {
        t.Errorf("Expected daily_report.txt to be written")
    }
}

func TestAutomationService_ProcessDailyReport_DatabaseError(t *testing.T) {
    // Test error handling
    mockDB := &MockDatabaseClient{
        queryError: fmt.Errorf("database connection failed"),
    }

    mockEmail := &MockEmailService{}
    mockFS := NewMockFileSystem()

    service := NewAutomationService(mockDB, mockEmail, mockFS, "TestService")

    err := service.ProcessDailyReport()

    if err == nil {
        t.Error("Expected error when database fails")
    }

    // Verify email was not sent when database fails
    if len(mockEmail.sentEmails) != 0 {
        t.Error("Expected no email when database fails")
    }
}

func main() {
    fmt.Println("=== Production Usage ===")

    // Production setup
    prodDB := &PostgresClient{connectionString: "postgres://..."}
    prodEmail := &SMTPEmailService{host: "smtp.company.com", port: 587}
    prodFS := &OSFileSystem{}

    prodService := NewAutomationService(prodDB, prodEmail, prodFS, "ProductionService")

    if err := prodService.ProcessDailyReport(); err != nil {
        log.Printf("Error processing daily report: %v", err)
    }

    fmt.Println("\n=== Testing Demonstration ===")

    // Run tests
    t := &testing.T{}
    TestAutomationService_ProcessDailyReport(t)
    TestAutomationService_ProcessDailyReport_DatabaseError(t)

    fmt.Println("Tests completed - check test output above")
}
```

**Prerequisites**: Session 22

---

### Session 24: Error Handling: Values, Variables, and Context

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master Go's error handling philosophy and patterns
- Understand error values, variables, and custom error types
- Learn to provide context through error types and behavior
- Apply robust error handling in automation systems
- Design APIs that respect error handling principles

**Videos Covered**:

- 6.1 Default Error Values (0:15:30)
- 6.2 Error Variables (0:12:45)
- 6.3 Type as Context (0:18:20)
- 6.4 Behavior as Context (0:22:15)

**Key Concepts**:

- Error interface: simple yet powerful design
- Error values vs error variables vs error types
- Providing context through custom error types
- Error behavior: temporary errors, timeout errors
- API design: giving users enough context for decisions
- Error handling as part of the happy path

**Hands-on Exercise**:

```go
// Comprehensive error handling in automation systems
package main

import (
    "fmt"
    "log"
    "net"
    "time"
)

// 1. Default Error Values - simple string errors
func processSimpleTask(taskID string) error {
    if taskID == "" {
        return fmt.Errorf("task ID cannot be empty")
    }

    if taskID == "invalid" {
        return fmt.Errorf("task %s is not valid", taskID)
    }

    fmt.Printf("Processing task: %s\n", taskID)
    return nil
}

// 2. Error Variables - predefined errors for comparison
var (
    ErrTaskNotFound     = fmt.Errorf("task not found")
    ErrTaskAlreadyExists = fmt.Errorf("task already exists")
    ErrInvalidTaskState = fmt.Errorf("invalid task state")
    ErrServiceUnavailable = fmt.Errorf("service unavailable")
)

func findTask(taskID string) error {
    switch taskID {
    case "missing":
        return ErrTaskNotFound
    case "duplicate":
        return ErrTaskAlreadyExists
    case "invalid-state":
        return ErrInvalidTaskState
    default:
        return nil
    }
}

// 3. Type as Context - custom error types with additional information
type ValidationError struct {
    Field   string
    Value   interface{}
    Message string
}

func (ve ValidationError) Error() string {
    return fmt.Sprintf("validation failed for field '%s' with value '%v': %s",
        ve.Field, ve.Value, ve.Message)
}

type NetworkError struct {
    Operation string
    Endpoint  string
    Err       error
    Timestamp time.Time
}

func (ne NetworkError) Error() string {
    return fmt.Sprintf("network error during %s to %s at %v: %v",
        ne.Operation, ne.Endpoint, ne.Timestamp, ne.Err)
}

// 4. Behavior as Context - errors that implement additional interfaces
type TemporaryError interface {
    error
    Temporary() bool
}

type TimeoutError interface {
    error
    Timeout() bool
}

type RetryableError interface {
    error
    Retryable() bool
    RetryAfter() time.Duration
}

// AutomationError implements multiple error behaviors
type AutomationError struct {
    Code      string
    Message   string
    Cause     error
    Timestamp time.Time
    isTemp    bool
    isTimeout bool
    retryable bool
    retryDelay time.Duration
}

func (ae AutomationError) Error() string {
    if ae.Cause != nil {
        return fmt.Sprintf("[%s] %s: %v", ae.Code, ae.Message, ae.Cause)
    }
    return fmt.Sprintf("[%s] %s", ae.Code, ae.Message)
}

func (ae AutomationError) Temporary() bool {
    return ae.isTemp
}

func (ae AutomationError) Timeout() bool {
    return ae.isTimeout
}

func (ae AutomationError) Retryable() bool {
    return ae.retryable
}

func (ae AutomationError) RetryAfter() time.Duration {
    return ae.retryDelay
}

func (ae AutomationError) Unwrap() error {
    return ae.Cause
}

// Factory functions for different error types
func NewValidationError(field string, value interface{}, message string) ValidationError {
    return ValidationError{
        Field:   field,
        Value:   value,
        Message: message,
    }
}

func NewNetworkError(operation, endpoint string, err error) NetworkError {
    return NetworkError{
        Operation: operation,
        Endpoint:  endpoint,
        Err:       err,
        Timestamp: time.Now(),
    }
}

func NewTemporaryError(code, message string) AutomationError {
    return AutomationError{
        Code:       code,
        Message:    message,
        Timestamp:  time.Now(),
        isTemp:     true,
        retryable:  true,
        retryDelay: 5 * time.Second,
    }
}

func NewTimeoutError(code, message string) AutomationError {
    return AutomationError{
        Code:      code,
        Message:   message,
        Timestamp: time.Now(),
        isTimeout: true,
        retryable: true,
        retryDelay: 10 * time.Second,
    }
}

// Automation service with comprehensive error handling
type AutomationService struct {
    name     string
    endpoint string
    timeout  time.Duration
}

func (as *AutomationService) ValidateConfig(config map[string]interface{}) error {
    if name, ok := config["name"].(string); !ok || name == "" {
        return NewValidationError("name", config["name"], "must be a non-empty string")
    }

    if timeout, ok := config["timeout"].(float64); !ok || timeout <= 0 {
        return NewValidationError("timeout", config["timeout"], "must be a positive number")
    }

    return nil
}

func (as *AutomationService) ConnectToService() error {
    // Simulate different types of connection errors
    switch as.endpoint {
    case "timeout.example.com":
        return NewTimeoutError("CONN_TIMEOUT", "connection timed out")
    case "temp-fail.example.com":
        return NewTemporaryError("TEMP_FAIL", "temporary connection failure")
    case "invalid.endpoint":
        return NewNetworkError("connect", as.endpoint, fmt.Errorf("invalid endpoint"))
    default:
        fmt.Printf("Connected to %s\n", as.endpoint)
        return nil
    }
}

func (as *AutomationService) ProcessWithRetry(data string) error {
    maxRetries := 3

    for attempt := 1; attempt <= maxRetries; attempt++ {
        err := as.processData(data)
        if err == nil {
            return nil
        }

        // Check if error is retryable
        if retryable, ok := err.(RetryableError); ok && retryable.Retryable() {
            if attempt < maxRetries {
                delay := retryable.RetryAfter()
                fmt.Printf("Attempt %d failed, retrying in %v: %v\n", attempt, delay, err)
                time.Sleep(delay)
                continue
            }
        }

        // Non-retryable error or max retries reached
        return fmt.Errorf("failed after %d attempts: %w", attempt, err)
    }

    return nil
}

func (as *AutomationService) processData(data string) error {
    // Simulate processing with different error scenarios
    switch data {
    case "timeout":
        return NewTimeoutError("PROC_TIMEOUT", "data processing timed out")
    case "temporary":
        return NewTemporaryError("PROC_TEMP", "temporary processing failure")
    case "invalid":
        return NewValidationError("data", data, "data format is invalid")
    default:
        fmt.Printf("Successfully processed: %s\n", data)
        return nil
    }
}

// Error handling utility functions
func handleError(err error) {
    if err == nil {
        return
    }

    fmt.Printf("Handling error: %v\n", err)

    // Check for specific error variables
    switch err {
    case ErrTaskNotFound:
        fmt.Println("  -> Task not found - creating new task")
        return
    case ErrServiceUnavailable:
        fmt.Println("  -> Service unavailable - switching to backup")
        return
    }

    // Check for error types
    var validationErr ValidationError
    if fmt.Errorf("%w", err) != nil && fmt.Sprintf("%T", err) == "main.ValidationError" {
        fmt.Printf("  -> Validation error in field: %s\n", validationErr.Field)
        return
    }

    var networkErr NetworkError
    if fmt.Errorf("%w", err) != nil && fmt.Sprintf("%T", err) == "main.NetworkError" {
        fmt.Printf("  -> Network error during: %s\n", networkErr.Operation)
        return
    }

    // Check for error behaviors
    if tempErr, ok := err.(TemporaryError); ok && tempErr.Temporary() {
        fmt.Println("  -> Temporary error - will retry")
        return
    }

    if timeoutErr, ok := err.(TimeoutError); ok && timeoutErr.Timeout() {
        fmt.Println("  -> Timeout error - increasing timeout")
        return
    }

    // Check for network errors (from net package)
    if netErr, ok := err.(net.Error); ok {
        if netErr.Temporary() {
            fmt.Println("  -> Network temporary error")
        }
        if netErr.Timeout() {
            fmt.Println("  -> Network timeout error")
        }
        return
    }

    fmt.Println("  -> Unknown error type - logging and continuing")
}

func main() {
    fmt.Println("=== Error Handling Demonstration ===")

    // 1. Simple error values
    fmt.Println("\n--- Simple Error Values ---")
    if err := processSimpleTask(""); err != nil {
        handleError(err)
    }

    if err := processSimpleTask("invalid"); err != nil {
        handleError(err)
    }

    // 2. Error variables
    fmt.Println("\n--- Error Variables ---")
    testCases := []string{"missing", "duplicate", "valid"}
    for _, taskID := range testCases {
        if err := findTask(taskID); err != nil {
            handleError(err)
        } else {
            fmt.Printf("Task %s found successfully\n", taskID)
        }
    }

    // 3. Type as context
    fmt.Println("\n--- Type as Context ---")
    service := &AutomationService{
        name:     "TestService",
        endpoint: "api.example.com",
        timeout:  30 * time.Second,
    }

    // Validation errors
    invalidConfig := map[string]interface{}{
        "name":    "",
        "timeout": -1,
    }

    if err := service.ValidateConfig(invalidConfig); err != nil {
        handleError(err)
    }

    // Network errors
    endpoints := []string{"timeout.example.com", "temp-fail.example.com", "invalid.endpoint", "valid.example.com"}
    for _, endpoint := range endpoints {
        service.endpoint = endpoint
        if err := service.ConnectToService(); err != nil {
            handleError(err)
        }
    }

    // 4. Behavior as context with retry logic
    fmt.Println("\n--- Behavior as Context with Retry ---")
    service.endpoint = "api.example.com"

    testData := []string{"timeout", "temporary", "invalid", "valid"}
    for _, data := range testData {
        fmt.Printf("\nProcessing data: %s\n", data)
        if err := service.ProcessWithRetry(data); err != nil {
            handleError(err)
        }
    }
}
```

**Prerequisites**: Session 23

---

### Session 25: Advanced Error Handling: Wrapping and Debugging

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master error wrapping and unwrapping with Go 1.13+ features
- Learn error chain analysis and debugging techniques
- Understand when and how to wrap errors effectively
- Apply advanced error handling patterns in automation systems
- Design error handling for complex automation workflows

**Videos Covered**:

- 6.5 Find the Bug (0:25:15)
- 6.6 Wrapping Errors (0:28:30)

**Key Concepts**:

- Error wrapping: fmt.Errorf with %w verb
- Error unwrapping: errors.Unwrap, errors.Is, errors.As
- Error chains: maintaining context through call stack
- Debugging with error chains
- When to wrap vs when to handle errors
- Error handling in complex automation pipelines

**Hands-on Exercise**:

```go
// Advanced error handling with wrapping and debugging
package main

import (
    "errors"
    "fmt"
    "log"
    "time"
)

// Custom error types for the automation system
type ConfigError struct {
    Component string
    Key       string
    Value     interface{}
    Reason    string
}

func (ce ConfigError) Error() string {
    return fmt.Sprintf("config error in %s: key '%s' with value '%v' - %s",
        ce.Component, ce.Key, ce.Value, ce.Reason)
}

type ProcessingError struct {
    Stage     string
    TaskID    string
    Timestamp time.Time
    Cause     error
}

func (pe ProcessingError) Error() string {
    return fmt.Sprintf("processing failed at stage '%s' for task '%s' at %v",
        pe.Stage, pe.TaskID, pe.Timestamp)
}

func (pe ProcessingError) Unwrap() error {
    return pe.Cause
}

// Predefined errors for comparison
var (
    ErrDatabaseConnection = errors.New("database connection failed")
    ErrInvalidCredentials = errors.New("invalid credentials")
    ErrRateLimitExceeded  = errors.New("rate limit exceeded")
    ErrServiceMaintenance = errors.New("service under maintenance")
)

// Low-level functions that can fail
func connectToDatabase(connectionString string) error {
    if connectionString == "" {
        return ErrDatabaseConnection
    }
    if connectionString == "invalid" {
        return fmt.Errorf("invalid connection string: %w", ErrDatabaseConnection)
    }
    return nil
}

func authenticateUser(username, password string) error {
    if username == "" || password == "" {
        return ErrInvalidCredentials
    }
    if username == "blocked" {
        return fmt.Errorf("user %s is blocked: %w", username, ErrInvalidCredentials)
    }
    return nil
}

func callExternalAPI(endpoint string) error {
    switch endpoint {
    case "rate-limited":
        return ErrRateLimitExceeded
    case "maintenance":
        return ErrServiceMaintenance
    case "network-error":
        return fmt.Errorf("network timeout connecting to %s", endpoint)
    default:
        return nil
    }
}

// Mid-level functions that wrap errors with context
func initializeDatabase(config map[string]string) error {
    connectionString, exists := config["db_connection"]
    if !exists {
        return ConfigError{
            Component: "database",
            Key:       "db_connection",
            Value:     nil,
            Reason:    "required configuration missing",
        }
    }

    if err := connectToDatabase(connectionString); err != nil {
        return fmt.Errorf("failed to initialize database: %w", err)
    }

    return nil
}

func authenticateService(config map[string]string) error {
    username := config["username"]
    password := config["password"]

    if err := authenticateUser(username, password); err != nil {
        return fmt.Errorf("service authentication failed for user %s: %w", username, err)
    }

    return nil
}

func fetchExternalData(endpoint string) ([]byte, error) {
    if err := callExternalAPI(endpoint); err != nil {
        return nil, fmt.Errorf("failed to fetch data from %s: %w", endpoint, err)
    }

    // Simulate successful data fetch
    return []byte(fmt.Sprintf("data from %s", endpoint)), nil
}

// High-level automation workflow
type AutomationWorkflow struct {
    Name   string
    Config map[string]string
    TaskID string
}

func (aw *AutomationWorkflow) Initialize() error {
    // Initialize database
    if err := initializeDatabase(aw.Config); err != nil {
        return fmt.Errorf("workflow %s initialization failed: %w", aw.Name, err)
    }

    // Authenticate service
    if err := authenticateService(aw.Config); err != nil {
        return fmt.Errorf("workflow %s authentication failed: %w", aw.Name, err)
    }

    return nil
}

func (aw *AutomationWorkflow) ProcessData(endpoint string) error {
    // Fetch external data
    data, err := fetchExternalData(endpoint)
    if err != nil {
        processingErr := ProcessingError{
            Stage:     "data_fetch",
            TaskID:    aw.TaskID,
            Timestamp: time.Now(),
            Cause:     err,
        }
        return fmt.Errorf("workflow %s processing failed: %w", aw.Name, processingErr)
    }

    // Simulate data processing
    if len(data) == 0 {
        processingErr := ProcessingError{
            Stage:     "data_validation",
            TaskID:    aw.TaskID,
            Timestamp: time.Now(),
            Cause:     errors.New("empty data received"),
        }
        return fmt.Errorf("workflow %s validation failed: %w", aw.Name, processingErr)
    }

    fmt.Printf("Successfully processed %d bytes of data\n", len(data))
    return nil
}

func (aw *AutomationWorkflow) Execute(endpoint string) error {
    // Initialize workflow
    if err := aw.Initialize(); err != nil {
        return fmt.Errorf("failed to execute workflow %s: %w", aw.Name, err)
    }

    // Process data
    if err := aw.ProcessData(endpoint); err != nil {
        return fmt.Errorf("failed to execute workflow %s: %w", aw.Name, err)
    }

    return nil
}

// Advanced error analysis functions
func analyzeError(err error) {
    if err == nil {
        return
    }

    fmt.Printf("\n=== Error Analysis ===\n")
    fmt.Printf("Error: %v\n", err)

    // Check for specific error values using errors.Is
    if errors.Is(err, ErrDatabaseConnection) {
        fmt.Println("Root cause: Database connection issue")
    }

    if errors.Is(err, ErrInvalidCredentials) {
        fmt.Println("Root cause: Authentication problem")
    }

    if errors.Is(err, ErrRateLimitExceeded) {
        fmt.Println("Root cause: Rate limiting")
    }

    if errors.Is(err, ErrServiceMaintenance) {
        fmt.Println("Root cause: Service maintenance")
    }

    // Extract specific error types using errors.As
    var configErr ConfigError
    if errors.As(err, &configErr) {
        fmt.Printf("Configuration issue found:\n")
        fmt.Printf("  Component: %s\n", configErr.Component)
        fmt.Printf("  Key: %s\n", configErr.Key)
        fmt.Printf("  Reason: %s\n", configErr.Reason)
    }

    var processingErr ProcessingError
    if errors.As(err, &processingErr) {
        fmt.Printf("Processing issue found:\n")
        fmt.Printf("  Stage: %s\n", processingErr.Stage)
        fmt.Printf("  Task ID: %s\n", processingErr.TaskID)
        fmt.Printf("  Timestamp: %v\n", processingErr.Timestamp)
    }

    // Walk the error chain
    fmt.Println("\nError chain:")
    current := err
    level := 0
    for current != nil {
        fmt.Printf("  %d: %v\n", level, current)
        current = errors.Unwrap(current)
        level++
        if level > 10 { // Prevent infinite loops
            break
        }
    }
}

func demonstrateErrorRecovery(err error) {
    fmt.Printf("\n=== Error Recovery Strategy ===\n")

    // Implement recovery strategies based on error type
    if errors.Is(err, ErrRateLimitExceeded) {
        fmt.Println("Recovery: Implementing exponential backoff")
        return
    }

    if errors.Is(err, ErrServiceMaintenance) {
        fmt.Println("Recovery: Switching to backup service")
        return
    }

    var configErr ConfigError
    if errors.As(err, &configErr) {
        fmt.Printf("Recovery: Using default value for %s.%s\n", configErr.Component, configErr.Key)
        return
    }

    var processingErr ProcessingError
    if errors.As(err, &processingErr) {
        fmt.Printf("Recovery: Retrying stage %s for task %s\n", processingErr.Stage, processingErr.TaskID)
        return
    }

    fmt.Println("Recovery: Logging error and continuing with degraded functionality")
}

func main() {
    fmt.Println("=== Advanced Error Handling Demonstration ===")

    // Test scenarios with different error conditions
    testScenarios := []struct {
        name     string
        config   map[string]string
        endpoint string
    }{
        {
            name: "Missing Database Config",
            config: map[string]string{
                "username": "admin",
                "password": "secret",
            },
            endpoint: "api.example.com",
        },
        {
            name: "Invalid Database Connection",
            config: map[string]string{
                "db_connection": "invalid",
                "username":      "admin",
                "password":      "secret",
            },
            endpoint: "api.example.com",
        },
        {
            name: "Authentication Failure",
            config: map[string]string{
                "db_connection": "postgres://localhost:5432/db",
                "username":      "blocked",
                "password":      "secret",
            },
            endpoint: "api.example.com",
        },
        {
            name: "Rate Limited API",
            config: map[string]string{
                "db_connection": "postgres://localhost:5432/db",
                "username":      "admin",
                "password":      "secret",
            },
            endpoint: "rate-limited",
        },
        {
            name: "Service Maintenance",
            config: map[string]string{
                "db_connection": "postgres://localhost:5432/db",
                "username":      "admin",
                "password":      "secret",
            },
            endpoint: "maintenance",
        },
        {
            name: "Successful Execution",
            config: map[string]string{
                "db_connection": "postgres://localhost:5432/db",
                "username":      "admin",
                "password":      "secret",
            },
            endpoint: "api.example.com",
        },
    }

    for i, scenario := range testScenarios {
        fmt.Printf("\n" + "="*50 + "\n")
        fmt.Printf("Scenario %d: %s\n", i+1, scenario.name)
        fmt.Printf("="*50 + "\n")

        workflow := &AutomationWorkflow{
            Name:   fmt.Sprintf("Workflow-%d", i+1),
            Config: scenario.config,
            TaskID: fmt.Sprintf("TASK-%03d", i+1),
        }

        err := workflow.Execute(scenario.endpoint)
        if err != nil {
            analyzeError(err)
            demonstrateErrorRecovery(err)
        } else {
            fmt.Println("✅ Workflow executed successfully!")
        }
    }

    fmt.Printf("\n" + "="*50 + "\n")
    fmt.Println("Error handling demonstration completed")
    fmt.Printf("="*50 + "\n")
}
```

**Prerequisites**: Session 24

---

### Session 26: Package Design and Language Mechanics

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand Go's package system and language mechanics
- Master package declaration, imports, and visibility rules
- Learn package initialization and dependency management
- Apply package design principles to automation systems
- Design clean, maintainable package structures

**Videos Covered**:

- 7.1 Language Mechanics (0:22:30)
- 7.2 Design Guidelines (0:18:45)

**Key Concepts**:

- Package declaration and naming conventions
- Import paths and package resolution
- Exported vs unexported identifiers
- Package initialization order and init functions
- Circular dependency prevention
- Package-level variables and constants

**Hands-on Exercise**:

```go
// Package design for automation system
// File: automation/config/config.go
package config

import (
    "encoding/json"
    "fmt"
    "os"
    "time"
)

// Exported types and constants
const (
    DefaultTimeout = 30 * time.Second
    MaxRetries     = 3
)

// Configuration represents automation system configuration
type Configuration struct {
    ServiceName     string        `json:"service_name"`
    Port           int           `json:"port"`
    DatabaseURL    string        `json:"database_url"`
    Timeout        time.Duration `json:"timeout"`
    EnableLogging  bool          `json:"enable_logging"`
    LogLevel       string        `json:"log_level"`
}

// Package-level variables (unexported)
var (
    defaultConfig *Configuration
    initialized   bool
)

// init function runs automatically when package is imported
func init() {
    defaultConfig = &Configuration{
        ServiceName:   "AutomationService",
        Port:         8080,
        Timeout:      DefaultTimeout,
        EnableLogging: true,
        LogLevel:     "INFO",
    }
    initialized = true
}

// Exported functions
func Load(filename string) (*Configuration, error) {
    data, err := os.ReadFile(filename)
    if err != nil {
        return nil, fmt.Errorf("failed to read config file: %w", err)
    }

    var config Configuration
    if err := json.Unmarshal(data, &config); err != nil {
        return nil, fmt.Errorf("failed to parse config: %w", err)
    }

    return &config, nil
}

func Default() *Configuration {
    if !initialized {
        panic("config package not initialized")
    }

    // Return a copy to prevent modification
    return &Configuration{
        ServiceName:   defaultConfig.ServiceName,
        Port:         defaultConfig.Port,
        DatabaseURL:  defaultConfig.DatabaseURL,
        Timeout:      defaultConfig.Timeout,
        EnableLogging: defaultConfig.EnableLogging,
        LogLevel:     defaultConfig.LogLevel,
    }
}

// File: automation/logger/logger.go
package logger

import (
    "fmt"
    "log"
    "os"
)

// Logger levels
const (
    DEBUG = "DEBUG"
    INFO  = "INFO"
    WARN  = "WARN"
    ERROR = "ERROR"
)

// Logger represents a structured logger
type Logger struct {
    level  string
    prefix string
    logger *log.Logger
}

// Package-level logger instance
var defaultLogger *Logger

func init() {
    defaultLogger = New(INFO, "AUTOMATION")
}

// New creates a new logger instance
func New(level, prefix string) *Logger {
    return &Logger{
        level:  level,
        prefix: prefix,
        logger: log.New(os.Stdout, fmt.Sprintf("[%s] ", prefix), log.LstdFlags),
    }
}

// Default returns the package-level logger
func Default() *Logger {
    return defaultLogger
}

func (l *Logger) Debug(message string) {
    if l.shouldLog(DEBUG) {
        l.logger.Printf("[%s] %s", DEBUG, message)
    }
}

func (l *Logger) Info(message string) {
    if l.shouldLog(INFO) {
        l.logger.Printf("[%s] %s", INFO, message)
    }
}

func (l *Logger) Warn(message string) {
    if l.shouldLog(WARN) {
        l.logger.Printf("[%s] %s", WARN, message)
    }
}

func (l *Logger) Error(message string) {
    if l.shouldLog(ERROR) {
        l.logger.Printf("[%s] %s", ERROR, message)
    }
}

// unexported helper method
func (l *Logger) shouldLog(level string) bool {
    levels := map[string]int{
        DEBUG: 0,
        INFO:  1,
        WARN:  2,
        ERROR: 3,
    }

    currentLevel, exists := levels[l.level]
    if !exists {
        return true
    }

    messageLevel, exists := levels[level]
    if !exists {
        return true
    }

    return messageLevel >= currentLevel
}

// File: main.go
package main

import (
    "automation/config"
    "automation/logger"
    "fmt"
)

func main() {
    fmt.Println("=== Package Design Demonstration ===")

    // Use default configuration
    cfg := config.Default()
    fmt.Printf("Default config: %+v\n", cfg)

    // Use package-level logger
    log := logger.Default()
    log.Info("Application starting")
    log.Debug("This debug message may not appear")

    // Create custom logger
    customLog := logger.New(logger.DEBUG, "CUSTOM")
    customLog.Debug("This debug message will appear")
    customLog.Error("Error message from custom logger")

    fmt.Println("Package demonstration completed")
}
```

**Prerequisites**: Session 25

---

### Session 27: Package-Oriented Design and Best Practices

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master package-oriented design principles
- Learn to organize code around business capabilities
- Understand package boundaries and dependencies
- Apply package design patterns to automation systems
- Design scalable, maintainable package architectures

**Videos Covered**:

- 7.3 Package-Oriented Design (0:35:20)

**Key Concepts**:

- Package-oriented design philosophy
- Organizing packages around business capabilities
- Package boundaries and cohesion
- Dependency management and inversion
- Package design patterns and anti-patterns
- Scalable package architectures

**Hands-on Exercise**:

```go
// Package-oriented design for automation platform
// Following business capability organization

// File: automation/core/task.go
package core

import (
    "context"
    "time"
)

// Task represents a unit of work in the automation system
type Task struct {
    ID          string
    Name        string
    Description string
    Status      TaskStatus
    CreatedAt   time.Time
    UpdatedAt   time.Time
}

type TaskStatus string

const (
    TaskStatusPending   TaskStatus = "pending"
    TaskStatusRunning   TaskStatus = "running"
    TaskStatusCompleted TaskStatus = "completed"
    TaskStatusFailed    TaskStatus = "failed"
)

// TaskRepository defines the interface for task persistence
type TaskRepository interface {
    Create(ctx context.Context, task *Task) error
    GetByID(ctx context.Context, id string) (*Task, error)
    Update(ctx context.Context, task *Task) error
    List(ctx context.Context, filter TaskFilter) ([]*Task, error)
}

// TaskFilter represents filtering criteria for tasks
type TaskFilter struct {
    Status    *TaskStatus
    CreatedAfter *time.Time
    Limit     int
}

// File: automation/core/processor.go
package core

import "context"

// Processor defines the interface for task processing
type Processor interface {
    Process(ctx context.Context, task *Task) error
    CanProcess(task *Task) bool
}

// ProcessorRegistry manages available processors
type ProcessorRegistry interface {
    Register(name string, processor Processor) error
    Get(name string) (Processor, error)
    List() []string
}

// File: automation/storage/memory.go
package storage

import (
    "automation/core"
    "context"
    "fmt"
    "sync"
    "time"
)

// MemoryTaskRepository implements core.TaskRepository using in-memory storage
type MemoryTaskRepository struct {
    mu    sync.RWMutex
    tasks map[string]*core.Task
}

func NewMemoryTaskRepository() *MemoryTaskRepository {
    return &MemoryTaskRepository{
        tasks: make(map[string]*core.Task),
    }
}

func (r *MemoryTaskRepository) Create(ctx context.Context, task *core.Task) error {
    r.mu.Lock()
    defer r.mu.Unlock()

    if _, exists := r.tasks[task.ID]; exists {
        return fmt.Errorf("task with ID %s already exists", task.ID)
    }

    task.CreatedAt = time.Now()
    task.UpdatedAt = time.Now()
    r.tasks[task.ID] = task

    return nil
}

func (r *MemoryTaskRepository) GetByID(ctx context.Context, id string) (*core.Task, error) {
    r.mu.RLock()
    defer r.mu.RUnlock()

    task, exists := r.tasks[id]
    if !exists {
        return nil, fmt.Errorf("task with ID %s not found", id)
    }

    return task, nil
}

func (r *MemoryTaskRepository) Update(ctx context.Context, task *core.Task) error {
    r.mu.Lock()
    defer r.mu.Unlock()

    if _, exists := r.tasks[task.ID]; !exists {
        return fmt.Errorf("task with ID %s not found", task.ID)
    }

    task.UpdatedAt = time.Now()
    r.tasks[task.ID] = task

    return nil
}

func (r *MemoryTaskRepository) List(ctx context.Context, filter core.TaskFilter) ([]*core.Task, error) {
    r.mu.RLock()
    defer r.mu.RUnlock()

    var result []*core.Task
    count := 0

    for _, task := range r.tasks {
        if filter.Status != nil && task.Status != *filter.Status {
            continue
        }

        if filter.CreatedAfter != nil && task.CreatedAt.Before(*filter.CreatedAfter) {
            continue
        }

        result = append(result, task)
        count++

        if filter.Limit > 0 && count >= filter.Limit {
            break
        }
    }

    return result, nil
}

// File: automation/processing/registry.go
package processing

import (
    "automation/core"
    "fmt"
    "sync"
)

// Registry implements core.ProcessorRegistry
type Registry struct {
    mu         sync.RWMutex
    processors map[string]core.Processor
}

func NewRegistry() *Registry {
    return &Registry{
        processors: make(map[string]core.Processor),
    }
}

func (r *Registry) Register(name string, processor core.Processor) error {
    r.mu.Lock()
    defer r.mu.Unlock()

    if _, exists := r.processors[name]; exists {
        return fmt.Errorf("processor %s already registered", name)
    }

    r.processors[name] = processor
    return nil
}

func (r *Registry) Get(name string) (core.Processor, error) {
    r.mu.RLock()
    defer r.mu.RUnlock()

    processor, exists := r.processors[name]
    if !exists {
        return nil, fmt.Errorf("processor %s not found", name)
    }

    return processor, nil
}

func (r *Registry) List() []string {
    r.mu.RLock()
    defer r.mu.RUnlock()

    var names []string
    for name := range r.processors {
        names = append(names, name)
    }

    return names
}

// File: automation/processing/file.go
package processing

import (
    "automation/core"
    "context"
    "fmt"
    "strings"
)

// FileProcessor processes file-related tasks
type FileProcessor struct {
    supportedExtensions []string
}

func NewFileProcessor(extensions []string) *FileProcessor {
    return &FileProcessor{
        supportedExtensions: extensions,
    }
}

func (fp *FileProcessor) Process(ctx context.Context, task *core.Task) error {
    fmt.Printf("Processing file task: %s\n", task.Name)

    // Simulate file processing
    task.Status = core.TaskStatusRunning

    // Simulate processing time
    select {
    case <-ctx.Done():
        return ctx.Err()
    default:
        // Processing logic here
        task.Status = core.TaskStatusCompleted
        return nil
    }
}

func (fp *FileProcessor) CanProcess(task *core.Task) bool {
    for _, ext := range fp.supportedExtensions {
        if strings.Contains(task.Name, ext) {
            return true
        }
    }
    return false
}

// File: automation/service/automation.go
package service

import (
    "automation/core"
    "context"
    "fmt"
)

// AutomationService orchestrates the automation system
type AutomationService struct {
    taskRepo  core.TaskRepository
    registry  core.ProcessorRegistry
}

func NewAutomationService(taskRepo core.TaskRepository, registry core.ProcessorRegistry) *AutomationService {
    return &AutomationService{
        taskRepo: taskRepo,
        registry: registry,
    }
}

func (s *AutomationService) CreateTask(ctx context.Context, name, description string) (*core.Task, error) {
    task := &core.Task{
        ID:          fmt.Sprintf("task-%d", len(name)), // Simple ID generation
        Name:        name,
        Description: description,
        Status:      core.TaskStatusPending,
    }

    if err := s.taskRepo.Create(ctx, task); err != nil {
        return nil, fmt.Errorf("failed to create task: %w", err)
    }

    return task, nil
}

func (s *AutomationService) ProcessTask(ctx context.Context, taskID string) error {
    task, err := s.taskRepo.GetByID(ctx, taskID)
    if err != nil {
        return fmt.Errorf("failed to get task: %w", err)
    }

    // Find suitable processor
    processors := s.registry.List()
    for _, processorName := range processors {
        processor, err := s.registry.Get(processorName)
        if err != nil {
            continue
        }

        if processor.CanProcess(task) {
            if err := processor.Process(ctx, task); err != nil {
                task.Status = core.TaskStatusFailed
                s.taskRepo.Update(ctx, task)
                return fmt.Errorf("processing failed: %w", err)
            }

            return s.taskRepo.Update(ctx, task)
        }
    }

    return fmt.Errorf("no suitable processor found for task %s", taskID)
}

// File: main.go
package main

import (
    "automation/core"
    "automation/processing"
    "automation/service"
    "automation/storage"
    "context"
    "fmt"
    "log"
)

func main() {
    fmt.Println("=== Package-Oriented Design Demonstration ===")

    // Initialize dependencies
    taskRepo := storage.NewMemoryTaskRepository()
    registry := processing.NewRegistry()

    // Register processors
    fileProcessor := processing.NewFileProcessor([]string{".txt", ".csv", ".json"})
    if err := registry.Register("file", fileProcessor); err != nil {
        log.Fatal(err)
    }

    // Create automation service
    automationService := service.NewAutomationService(taskRepo, registry)

    ctx := context.Background()

    // Create and process tasks
    task1, err := automationService.CreateTask(ctx, "process_data.csv", "Process CSV data file")
    if err != nil {
        log.Fatal(err)
    }

    task2, err := automationService.CreateTask(ctx, "backup_logs.txt", "Backup log files")
    if err != nil {
        log.Fatal(err)
    }

    // Process tasks
    fmt.Printf("Processing task 1: %s\n", task1.ID)
    if err := automationService.ProcessTask(ctx, task1.ID); err != nil {
        log.Printf("Task 1 failed: %v", err)
    }

    fmt.Printf("Processing task 2: %s\n", task2.ID)
    if err := automationService.ProcessTask(ctx, task2.ID); err != nil {
        log.Printf("Task 2 failed: %v", err)
    }

    // List completed tasks
    completedStatus := core.TaskStatusCompleted
    filter := core.TaskFilter{
        Status: &completedStatus,
        Limit:  10,
    }

    completedTasks, err := taskRepo.List(ctx, filter)
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("\nCompleted tasks: %d\n", len(completedTasks))
    for _, task := range completedTasks {
        fmt.Printf("- %s: %s\n", task.ID, task.Name)
    }
}
```

**Prerequisites**: Session 26

---

### Session 28: Scheduler Mechanics and Goroutines

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand OS scheduler vs Go scheduler mechanics
- Master goroutine creation and lifecycle management
- Learn about M:N threading model and work stealing
- Apply concurrent patterns in automation systems
- Design efficient concurrent automation workflows

**Videos Covered**:

- 8.1 OS Scheduler (0:18:30)
- 8.2 Go Scheduler (0:22:15)
- 8.3 Creating Goroutines (0:16:45)

**Key Concepts**:

- OS threads vs goroutines: lightweight concurrency
- M:N threading model: mapping goroutines to OS threads
- Go scheduler: work stealing and cooperative scheduling
- Goroutine creation cost and lifecycle
- Context switching and stack management
- Concurrency vs parallelism

**Hands-on Exercise**:

```go
// Goroutines and scheduler mechanics in automation
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

// Task represents work to be done concurrently
type Task struct {
    ID       int
    Name     string
    Duration time.Duration
    Result   string
}

// Worker function that processes tasks
func processTask(task Task, results chan<- Task, wg *sync.WaitGroup) {
    defer wg.Done()

    fmt.Printf("Goroutine processing task %d: %s\n", task.ID, task.Name)

    // Simulate work
    time.Sleep(task.Duration)

    task.Result = fmt.Sprintf("Completed task %d", task.ID)
    results <- task
}

// Demonstrate goroutine creation and management
func demonstrateGoroutines() {
    fmt.Println("=== Goroutine Creation Demonstration ===")

    tasks := []Task{
        {ID: 1, Name: "Process CSV", Duration: 100 * time.Millisecond},
        {ID: 2, Name: "Send Email", Duration: 200 * time.Millisecond},
        {ID: 3, Name: "Update Database", Duration: 150 * time.Millisecond},
        {ID: 4, Name: "Generate Report", Duration: 300 * time.Millisecond},
        {ID: 5, Name: "Backup Files", Duration: 250 * time.Millisecond},
    }

    var wg sync.WaitGroup
    results := make(chan Task, len(tasks))

    start := time.Now()

    // Launch goroutines for each task
    for _, task := range tasks {
        wg.Add(1)
        go processTask(task, results, &wg)
    }

    // Wait for all goroutines to complete
    wg.Wait()
    close(results)

    duration := time.Since(start)

    // Collect results
    fmt.Println("\nResults:")
    for result := range results {
        fmt.Printf("- %s\n", result.Result)
    }

    fmt.Printf("\nTotal execution time: %v\n", duration)
    fmt.Printf("Number of goroutines: %d\n", runtime.NumGoroutine())
}

// AutomationPipeline demonstrates concurrent pipeline processing
type AutomationPipeline struct {
    Name        string
    Stages      []string
    Concurrency int
}

func (ap *AutomationPipeline) ProcessItems(items []string) {
    fmt.Printf("\n=== Processing %d items with %d concurrent workers ===\n",
        len(items), ap.Concurrency)

    // Create channels for pipeline stages
    input := make(chan string, len(items))
    output := make(chan string, len(items))

    // Fill input channel
    for _, item := range items {
        input <- item
    }
    close(input)

    var wg sync.WaitGroup

    // Start concurrent workers
    for i := 0; i < ap.Concurrency; i++ {
        wg.Add(1)
        go ap.worker(i, input, output, &wg)
    }

    // Close output channel when all workers are done
    go func() {
        wg.Wait()
        close(output)
    }()

    // Collect results
    var results []string
    for result := range output {
        results = append(results, result)
    }

    fmt.Printf("Processed %d items successfully\n", len(results))
    for _, result := range results {
        fmt.Printf("- %s\n", result)
    }
}

func (ap *AutomationPipeline) worker(id int, input <-chan string, output chan<- string, wg *sync.WaitGroup) {
    defer wg.Done()

    for item := range input {
        // Simulate processing through pipeline stages
        result := item
        for _, stage := range ap.Stages {
            result = ap.processStage(stage, result, id)
            // Yield to scheduler occasionally
            runtime.Gosched()
        }

        output <- result
    }
}

func (ap *AutomationPipeline) processStage(stage, item string, workerID int) string {
    // Simulate stage processing time
    time.Sleep(50 * time.Millisecond)
    return fmt.Sprintf("%s->%s(w%d)", item, stage, workerID)
}

// Demonstrate scheduler behavior with CPU-bound vs I/O-bound tasks
func demonstrateSchedulerBehavior() {
    fmt.Println("\n=== Scheduler Behavior Demonstration ===")

    fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))
    fmt.Printf("NumCPU: %d\n", runtime.NumCPU())

    // CPU-bound task
    cpuBoundTask := func(id int, duration time.Duration) {
        start := time.Now()
        end := start.Add(duration)

        for time.Now().Before(end) {
            // CPU-intensive work (busy loop)
            for i := 0; i < 1000; i++ {
                _ = i * i
            }
        }

        fmt.Printf("CPU-bound task %d completed in %v\n", id, time.Since(start))
    }

    // I/O-bound task
    ioBoundTask := func(id int, duration time.Duration) {
        start := time.Now()

        // Simulate I/O operation
        time.Sleep(duration)

        fmt.Printf("I/O-bound task %d completed in %v\n", id, time.Since(start))
    }

    var wg sync.WaitGroup

    fmt.Println("\nStarting CPU-bound tasks:")
    start := time.Now()
    for i := 0; i < 4; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            cpuBoundTask(id, 200*time.Millisecond)
        }(i)
    }
    wg.Wait()
    fmt.Printf("CPU-bound tasks completed in: %v\n", time.Since(start))

    fmt.Println("\nStarting I/O-bound tasks:")
    start = time.Now()
    for i := 0; i < 4; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            ioBoundTask(id, 200*time.Millisecond)
        }(i)
    }
    wg.Wait()
    fmt.Printf("I/O-bound tasks completed in: %v\n", time.Since(start))
}

func main() {
    fmt.Printf("Starting with %d goroutines\n", runtime.NumGoroutine())

    // Demonstrate basic goroutine usage
    demonstrateGoroutines()

    // Demonstrate concurrent pipeline
    pipeline := &AutomationPipeline{
        Name:        "DataProcessingPipeline",
        Stages:      []string{"validate", "transform", "store"},
        Concurrency: 3,
    }

    items := []string{"data1", "data2", "data3", "data4", "data5", "data6"}
    pipeline.ProcessItems(items)

    // Demonstrate scheduler behavior
    demonstrateSchedulerBehavior()

    fmt.Printf("\nEnding with %d goroutines\n", runtime.NumGoroutine())
}
```

**Prerequisites**: Session 27

---

### Session 29: Data Races and Synchronization

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Understand data races and their dangers in concurrent programs
- Master synchronization primitives: mutexes, atomic operations
- Learn race detection tools and techniques
- Apply safe concurrency patterns in automation systems
- Design race-free automation components

**Videos Covered**:

- 9.1 Cache Coherency and False Sharing (0:25:30)
- 9.2 Atomic Functions (0:18:45)
- 9.3 Mutexes (0:22:15)
- 9.4 Read/Write Mutexes (0:16:30)
- 9.5 Map Mutations (0:14:20)
- 9.6 Interface-Based Race Conditions (0:19:45)

**Key Concepts**:

- Data races: definition and consequences
- Cache coherency and false sharing
- Atomic operations: when and how to use them
- Mutexes: exclusive access to shared resources
- Read/write mutexes: optimizing for read-heavy workloads
- Race detection: tools and techniques
- Safe patterns for concurrent access

**Hands-on Exercise**:

```go
// Data races and synchronization in automation systems
package main

import (
    "fmt"
    "math/rand"
    "runtime"
    "sync"
    "sync/atomic"
    "time"
)

// UNSAFE: Counter with data race
type UnsafeCounter struct {
    value int64
}

func (c *UnsafeCounter) Increment() {
    c.value++ // DATA RACE!
}

func (c *UnsafeCounter) Value() int64 {
    return c.value // DATA RACE!
}

// SAFE: Counter using atomic operations
type AtomicCounter struct {
    value int64
}

func (c *AtomicCounter) Increment() {
    atomic.AddInt64(&c.value, 1)
}

func (c *AtomicCounter) Value() int64 {
    return atomic.LoadInt64(&c.value)
}

// SAFE: Counter using mutex
type MutexCounter struct {
    mu    sync.Mutex
    value int64
}

func (c *MutexCounter) Increment() {
    c.mu.Lock()
    c.value++
    c.mu.Unlock()
}

func (c *MutexCounter) Value() int64 {
    c.mu.Lock()
    defer c.mu.Unlock()
    return c.value
}

// Automation metrics with different synchronization approaches
type AutomationMetrics struct {
    // Atomic counters for simple metrics
    tasksProcessed int64
    errorsOccurred int64

    // Mutex-protected complex state
    mu           sync.RWMutex
    tasksByType  map[string]int64
    lastActivity time.Time

    // Read-heavy data with RWMutex
    configMu sync.RWMutex
    config   map[string]string
}

func NewAutomationMetrics() *AutomationMetrics {
    return &AutomationMetrics{
        tasksByType: make(map[string]int64),
        config:      make(map[string]string),
        lastActivity: time.Now(),
    }
}

// Atomic operations for simple counters
func (am *AutomationMetrics) IncrementTasksProcessed() {
    atomic.AddInt64(&am.tasksProcessed, 1)
}

func (am *AutomationMetrics) IncrementErrors() {
    atomic.AddInt64(&am.errorsOccurred, 1)
}

func (am *AutomationMetrics) GetTasksProcessed() int64 {
    return atomic.LoadInt64(&am.tasksProcessed)
}

func (am *AutomationMetrics) GetErrors() int64 {
    return atomic.LoadInt64(&am.errorsOccurred)
}

// Mutex for complex state modifications
func (am *AutomationMetrics) RecordTaskByType(taskType string) {
    am.mu.Lock()
    am.tasksByType[taskType]++
    am.lastActivity = time.Now()
    am.mu.Unlock()
}

func (am *AutomationMetrics) GetTasksByType() map[string]int64 {
    am.mu.RLock()
    defer am.mu.RUnlock()

    // Return a copy to prevent races
    result := make(map[string]int64)
    for k, v := range am.tasksByType {
        result[k] = v
    }
    return result
}

func (am *AutomationMetrics) GetLastActivity() time.Time {
    am.mu.RLock()
    defer am.mu.RUnlock()
    return am.lastActivity
}

// RWMutex for read-heavy configuration
func (am *AutomationMetrics) UpdateConfig(key, value string) {
    am.configMu.Lock()
    am.config[key] = value
    am.configMu.Unlock()
}

func (am *AutomationMetrics) GetConfig(key string) (string, bool) {
    am.configMu.RLock()
    defer am.configMu.RUnlock()
    value, exists := am.config[key]
    return value, exists
}

func (am *AutomationMetrics) GetAllConfig() map[string]string {
    am.configMu.RLock()
    defer am.configMu.RUnlock()

    result := make(map[string]string)
    for k, v := range am.config {
        result[k] = v
    }
    return result
}

// Demonstrate data races
func demonstrateDataRaces() {
    fmt.Println("=== Data Race Demonstration ===")

    const numGoroutines = 100
    const incrementsPerGoroutine = 1000

    // Test unsafe counter (will have data races)
    fmt.Println("\nTesting unsafe counter (with data races):")
    unsafeCounter := &UnsafeCounter{}

    var wg sync.WaitGroup
    start := time.Now()

    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < incrementsPerGoroutine; j++ {
                unsafeCounter.Increment()
            }
        }()
    }

    wg.Wait()
    unsafeDuration := time.Since(start)
    expectedValue := int64(numGoroutines * incrementsPerGoroutine)
    actualValue := unsafeCounter.Value()

    fmt.Printf("Expected: %d, Actual: %d, Correct: %t, Duration: %v\n",
        expectedValue, actualValue, expectedValue == actualValue, unsafeDuration)

    // Test atomic counter
    fmt.Println("\nTesting atomic counter:")
    atomicCounter := &AtomicCounter{}

    start = time.Now()
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < incrementsPerGoroutine; j++ {
                atomicCounter.Increment()
            }
        }()
    }

    wg.Wait()
    atomicDuration := time.Since(start)
    actualValue = atomicCounter.Value()

    fmt.Printf("Expected: %d, Actual: %d, Correct: %t, Duration: %v\n",
        expectedValue, actualValue, expectedValue == actualValue, atomicDuration)

    // Test mutex counter
    fmt.Println("\nTesting mutex counter:")
    mutexCounter := &MutexCounter{}

    start = time.Now()
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < incrementsPerGoroutine; j++ {
                mutexCounter.Increment()
            }
        }()
    }

    wg.Wait()
    mutexDuration := time.Since(start)
    actualValue = mutexCounter.Value()

    fmt.Printf("Expected: %d, Actual: %d, Correct: %t, Duration: %v\n",
        expectedValue, actualValue, expectedValue == actualValue, mutexDuration)

    fmt.Printf("\nPerformance comparison:\n")
    fmt.Printf("Unsafe (with races): %v\n", unsafeDuration)
    fmt.Printf("Atomic: %v (%.2fx slower)\n", atomicDuration, float64(atomicDuration)/float64(unsafeDuration))
    fmt.Printf("Mutex: %v (%.2fx slower)\n", mutexDuration, float64(mutexDuration)/float64(unsafeDuration))
}

// Simulate automation workload with metrics
func simulateAutomationWorkload() {
    fmt.Println("\n=== Automation Workload Simulation ===")

    metrics := NewAutomationMetrics()

    // Set initial configuration
    metrics.UpdateConfig("max_workers", "10")
    metrics.UpdateConfig("timeout", "30s")
    metrics.UpdateConfig("retry_count", "3")

    const numWorkers = 20
    const tasksPerWorker = 50

    var wg sync.WaitGroup

    // Start workers that process tasks
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()

            taskTypes := []string{"file_process", "email_send", "db_update", "report_gen"}

            for j := 0; j < tasksPerWorker; j++ {
                // Simulate task processing
                taskType := taskTypes[rand.Intn(len(taskTypes))]

                // Record metrics
                metrics.IncrementTasksProcessed()
                metrics.RecordTaskByType(taskType)

                // Simulate occasional errors
                if rand.Float32() < 0.1 { // 10% error rate
                    metrics.IncrementErrors()
                }

                // Simulate work
                time.Sleep(time.Millisecond)

                // Occasionally read configuration (read-heavy workload)
                if j%10 == 0 {
                    if timeout, exists := metrics.GetConfig("timeout"); exists {
                        _ = timeout // Use the config value
                    }
                }
            }
        }(i)
    }

    // Start a monitoring goroutine
    wg.Add(1)
    go func() {
        defer wg.Done()

        ticker := time.NewTicker(100 * time.Millisecond)
        defer ticker.Stop()

        for i := 0; i < 10; i++ {
            <-ticker.C

            processed := metrics.GetTasksProcessed()
            errors := metrics.GetErrors()
            lastActivity := metrics.GetLastActivity()

            fmt.Printf("Monitor: Processed=%d, Errors=%d, LastActivity=%v ago\n",
                processed, errors, time.Since(lastActivity))
        }
    }()

    wg.Wait()

    // Final report
    fmt.Println("\nFinal Metrics:")
    fmt.Printf("Total tasks processed: %d\n", metrics.GetTasksProcessed())
    fmt.Printf("Total errors: %d\n", metrics.GetErrors())

    tasksByType := metrics.GetTasksByType()
    fmt.Println("Tasks by type:")
    for taskType, count := range tasksByType {
        fmt.Printf("  %s: %d\n", taskType, count)
    }

    config := metrics.GetAllConfig()
    fmt.Println("Configuration:")
    for key, value := range config {
        fmt.Printf("  %s: %s\n", key, value)
    }
}

func main() {
    fmt.Printf("Starting with GOMAXPROCS=%d\n", runtime.GOMAXPROCS(0))

    // Demonstrate data races and synchronization
    demonstrateDataRaces()

    // Simulate realistic automation workload
    simulateAutomationWorkload()

    fmt.Println("\nNote: Run with 'go run -race main.go' to detect data races")
}
```

**Prerequisites**: Session 28

---

### Session 30: Channels and Signaling Semantics

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master channel creation, operations, and signaling semantics
- Understand buffered vs unbuffered channels
- Learn channel patterns: fan-out, fan-in, worker pools
- Apply channel-based communication in automation systems
- Design robust concurrent automation pipelines

**Videos Covered**:

- 10.1 Signaling Semantics (0:28:15)
- 10.2 Basic Patterns (0:22:30)
- 10.3 Pooling Pattern (0:18:45)
- 10.4 Fan Out Pattern (0:16:20)

**Key Concepts**:

- Channels: Go's communication mechanism
- Signaling semantics: guarantee vs no guarantee
- Buffered vs unbuffered channels
- Channel operations: send, receive, close
- Channel patterns: pooling, fan-out, fan-in
- Select statement for non-blocking operations
- Channel-based synchronization

**Hands-on Exercise**:

```go
// Channels and signaling patterns in automation systems
package main

import (
    "fmt"
    "math/rand"
    "sync"
    "time"
)

// Task represents work to be processed
type Task struct {
    ID       int
    Type     string
    Data     string
    Priority int
}

// Result represents the outcome of task processing
type Result struct {
    TaskID    int
    Success   bool
    Output    string
    Duration  time.Duration
    ProcessedBy int
}

// Basic channel communication
func demonstrateBasicChannels() {
    fmt.Println("=== Basic Channel Communication ===")

    // Unbuffered channel - synchronous communication
    unbuffered := make(chan string)

    go func() {
        fmt.Println("Goroutine: Sending message...")
        unbuffered <- "Hello from goroutine!"
        fmt.Println("Goroutine: Message sent")
    }()

    fmt.Println("Main: Waiting for message...")
    message := <-unbuffered
    fmt.Printf("Main: Received: %s\n", message)

    // Buffered channel - asynchronous communication
    buffered := make(chan int, 3)

    // Send multiple values without blocking
    buffered <- 1
    buffered <- 2
    buffered <- 3

    fmt.Printf("Buffered channel length: %d, capacity: %d\n", len(buffered), cap(buffered))

    // Receive values
    for i := 0; i < 3; i++ {
        value := <-buffered
        fmt.Printf("Received from buffered channel: %d\n", value)
    }
}

// Worker pool pattern
func demonstrateWorkerPool() {
    fmt.Println("\n=== Worker Pool Pattern ===")

    const numWorkers = 3
    const numTasks = 10

    // Create channels
    tasks := make(chan Task, numTasks)
    results := make(chan Result, numTasks)

    // Start workers
    var wg sync.WaitGroup
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go worker(i, tasks, results, &wg)
    }

    // Send tasks
    go func() {
        for i := 0; i < numTasks; i++ {
            task := Task{
                ID:       i,
                Type:     fmt.Sprintf("type_%d", i%3),
                Data:     fmt.Sprintf("data_%d", i),
                Priority: rand.Intn(10),
            }
            tasks <- task
        }
        close(tasks) // Signal no more tasks
    }()

    // Close results channel when all workers are done
    go func() {
        wg.Wait()
        close(results)
    }()

    // Collect results
    var allResults []Result
    for result := range results {
        allResults = append(allResults, result)
    }

    fmt.Printf("Processed %d tasks with %d workers\n", len(allResults), numWorkers)
    for _, result := range allResults {
        status := "SUCCESS"
        if !result.Success {
            status = "FAILED"
        }
        fmt.Printf("Task %d: %s (worker %d, %v)\n",
            result.TaskID, status, result.ProcessedBy, result.Duration)
    }
}

func worker(id int, tasks <-chan Task, results chan<- Result, wg *sync.WaitGroup) {
    defer wg.Done()

    for task := range tasks {
        start := time.Now()

        // Simulate task processing
        processingTime := time.Duration(rand.Intn(200)+50) * time.Millisecond
        time.Sleep(processingTime)

        // Simulate occasional failures
        success := rand.Float32() > 0.2 // 80% success rate

        result := Result{
            TaskID:      task.ID,
            Success:     success,
            Output:      fmt.Sprintf("Processed %s by worker %d", task.Data, id),
            Duration:    time.Since(start),
            ProcessedBy: id,
        }

        results <- result
    }
}

// Fan-out pattern: distribute work to multiple goroutines
func demonstrateFanOut() {
    fmt.Println("\n=== Fan-Out Pattern ===")

    // Input channel
    input := make(chan int, 10)

    // Output channels for different processors
    evenNumbers := make(chan int, 5)
    oddNumbers := make(chan int, 5)
    primeNumbers := make(chan int, 5)

    // Fan-out: distribute input to multiple processors
    go func() {
        defer close(evenNumbers)
        defer close(oddNumbers)
        defer close(primeNumbers)

        for num := range input {
            if num%2 == 0 {
                evenNumbers <- num
            } else {
                oddNumbers <- num
            }

            if isPrime(num) {
                primeNumbers <- num
            }
        }
    }()

    // Processors
    var wg sync.WaitGroup

    // Even number processor
    wg.Add(1)
    go func() {
        defer wg.Done()
        fmt.Println("Even numbers:")
        for num := range evenNumbers {
            fmt.Printf("  Even: %d\n", num)
        }
    }()

    // Odd number processor
    wg.Add(1)
    go func() {
        defer wg.Done()
        fmt.Println("Odd numbers:")
        for num := range oddNumbers {
            fmt.Printf("  Odd: %d\n", num)
        }
    }()

    // Prime number processor
    wg.Add(1)
    go func() {
        defer wg.Done()
        fmt.Println("Prime numbers:")
        for num := range primeNumbers {
            fmt.Printf("  Prime: %d\n", num)
        }
    }()

    // Send input data
    numbers := []int{2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15}
    for _, num := range numbers {
        input <- num
    }
    close(input)

    wg.Wait()
}

// Fan-in pattern: combine multiple channels into one
func demonstrateFanIn() {
    fmt.Println("\n=== Fan-In Pattern ===")

    // Create multiple input channels
    source1 := make(chan string)
    source2 := make(chan string)
    source3 := make(chan string)

    // Fan-in: combine multiple sources
    combined := fanIn(source1, source2, source3)

    // Start sources
    go func() {
        defer close(source1)
        for i := 0; i < 3; i++ {
            source1 <- fmt.Sprintf("Source1-Message%d", i)
            time.Sleep(100 * time.Millisecond)
        }
    }()

    go func() {
        defer close(source2)
        for i := 0; i < 3; i++ {
            source2 <- fmt.Sprintf("Source2-Message%d", i)
            time.Sleep(150 * time.Millisecond)
        }
    }()

    go func() {
        defer close(source3)
        for i := 0; i < 3; i++ {
            source3 <- fmt.Sprintf("Source3-Message%d", i)
            time.Sleep(200 * time.Millisecond)
        }
    }()

    // Receive combined messages
    for message := range combined {
        fmt.Printf("Combined: %s\n", message)
    }
}

func fanIn(channels ...<-chan string) <-chan string {
    out := make(chan string)
    var wg sync.WaitGroup

    // Start a goroutine for each input channel
    for _, ch := range channels {
        wg.Add(1)
        go func(c <-chan string) {
            defer wg.Done()
            for msg := range c {
                out <- msg
            }
        }(ch)
    }

    // Close output channel when all inputs are done
    go func() {
        wg.Wait()
        close(out)
    }()

    return out
}

// Automation pipeline using channels
type AutomationPipeline struct {
    Name     string
    Stages   []string
    Workers  int
}

func (ap *AutomationPipeline) Process(items []string) {
    fmt.Printf("\n=== Automation Pipeline: %s ===\n", ap.Name)

    // Create pipeline stages
    input := make(chan string, len(items))
    stages := make([]chan string, len(ap.Stages)+1)

    for i := range stages {
        stages[i] = make(chan string, 10)
    }

    // Fill input
    for _, item := range items {
        input <- item
    }
    close(input)
    stages[0] = input

    // Create processing stages
    for i, stage := range ap.Stages {
        go ap.processStage(stage, stages[i], stages[i+1])
    }

    // Collect final results
    var results []string
    for result := range stages[len(ap.Stages)] {
        results = append(results, result)
    }

    fmt.Printf("Pipeline processed %d items:\n", len(results))
    for _, result := range results {
        fmt.Printf("  %s\n", result)
    }
}

func (ap *AutomationPipeline) processStage(stageName string, input <-chan string, output chan<- string) {
    defer close(output)

    for item := range input {
        // Simulate stage processing
        time.Sleep(50 * time.Millisecond)
        processed := fmt.Sprintf("%s->%s", item, stageName)
        output <- processed
    }
}

// Select statement for non-blocking operations
func demonstrateSelect() {
    fmt.Println("\n=== Select Statement ===")

    ch1 := make(chan string)
    ch2 := make(chan string)
    timeout := time.After(2 * time.Second)

    // Start goroutines that send at different times
    go func() {
        time.Sleep(500 * time.Millisecond)
        ch1 <- "Message from channel 1"
    }()

    go func() {
        time.Sleep(1 * time.Second)
        ch2 <- "Message from channel 2"
    }()

    // Use select to handle multiple channels
    for i := 0; i < 3; i++ {
        select {
        case msg1 := <-ch1:
            fmt.Printf("Received from ch1: %s\n", msg1)
        case msg2 := <-ch2:
            fmt.Printf("Received from ch2: %s\n", msg2)
        case <-timeout:
            fmt.Println("Timeout reached")
            return
        default:
            fmt.Println("No channels ready, doing other work...")
            time.Sleep(300 * time.Millisecond)
        }
    }
}

func isPrime(n int) bool {
    if n < 2 {
        return false
    }
    for i := 2; i*i <= n; i++ {
        if n%i == 0 {
            return false
        }
    }
    return true
}

func main() {
    // Demonstrate basic channel operations
    demonstrateBasicChannels()

    // Demonstrate worker pool pattern
    demonstrateWorkerPool()

    // Demonstrate fan-out pattern
    demonstrateFanOut()

    // Demonstrate fan-in pattern
    demonstrateFanIn()

    // Demonstrate automation pipeline
    pipeline := &AutomationPipeline{
        Name:    "DataProcessingPipeline",
        Stages:  []string{"validate", "transform", "enrich", "store"},
        Workers: 2,
    }

    items := []string{"data1", "data2", "data3", "data4", "data5"}
    pipeline.Process(items)

    // Demonstrate select statement
    demonstrateSelect()
}
```

**Prerequisites**: Session 29

---

### Session 31: Advanced Channel Patterns and Context

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master advanced channel patterns: drop, cancellation, context
- Understand context package for cancellation and timeouts
- Learn channel-based concurrency patterns
- Apply advanced patterns in automation systems
- Design robust, cancellable automation workflows

**Videos Covered**:

- 10.5 Drop Pattern (0:14:30)
- 10.6 Cancellation Pattern (0:18:45)
- 11.1 Context (0:22:15)
- 11.2 Failure Detection (0:16:30)

**Key Concepts**:

- Drop pattern: handling overload gracefully
- Cancellation: stopping work in progress
- Context package: cancellation, deadlines, values
- Failure detection and recovery patterns
- Graceful shutdown patterns
- Resource cleanup with defer and context

**Hands-on Exercise**:

```go
// Advanced channel patterns and context in automation
package main

import (
    "context"
    "fmt"
    "log"
    "math/rand"
    "sync"
    "time"
)

// Task represents work with context support
type Task struct {
    ID       string
    Type     string
    Data     interface{}
    Priority int
    Timeout  time.Duration
}

// Result represents task execution result
type Result struct {
    TaskID   string
    Success  bool
    Output   interface{}
    Error    error
    Duration time.Duration
}

// Drop pattern: handle overload by dropping tasks
func demonstrateDropPattern() {
    fmt.Println("=== Drop Pattern Demonstration ===")

    const bufferSize = 3
    const numTasks = 10

    tasks := make(chan Task, bufferSize)
    results := make(chan Result, numTasks)
    dropped := make(chan Task, numTasks)

    // Producer: generates tasks faster than they can be processed
    go func() {
        defer close(tasks)

        for i := 0; i < numTasks; i++ {
            task := Task{
                ID:   fmt.Sprintf("task-%d", i),
                Type: "heavy_processing",
                Data: fmt.Sprintf("data-%d", i),
            }

            select {
            case tasks <- task:
                fmt.Printf("Queued task: %s\n", task.ID)
            default:
                // Channel is full, drop the task
                dropped <- task
                fmt.Printf("DROPPED task: %s (queue full)\n", task.ID)
            }

            time.Sleep(50 * time.Millisecond) // Fast producer
        }
    }()

    // Worker: processes tasks slowly
    go func() {
        defer close(results)

        for task := range tasks {
            start := time.Now()

            // Simulate slow processing
            time.Sleep(200 * time.Millisecond)

            result := Result{
                TaskID:   task.ID,
                Success:  true,
                Output:   fmt.Sprintf("processed-%s", task.Data),
                Duration: time.Since(start),
            }

            results <- result
        }
    }()

    // Collect results and dropped tasks
    go func() {
        defer close(dropped)
        time.Sleep(2 * time.Second) // Let producer finish
    }()

    var processedCount, droppedCount int

    // Use select to handle both channels
    for {
        select {
        case result, ok := <-results:
            if !ok {
                results = nil
            } else {
                fmt.Printf("Processed: %s in %v\n", result.TaskID, result.Duration)
                processedCount++
            }
        case task, ok := <-dropped:
            if !ok {
                dropped = nil
            } else {
                fmt.Printf("Confirmed dropped: %s\n", task.ID)
                droppedCount++
            }
        }

        if results == nil && dropped == nil {
            break
        }
    }

    fmt.Printf("Summary: Processed=%d, Dropped=%d, Total=%d\n",
        processedCount, droppedCount, processedCount+droppedCount)
}

// Cancellation pattern with context
func demonstrateCancellationPattern() {
    fmt.Println("\n=== Cancellation Pattern Demonstration ===")

    // Create context with timeout
    ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
    defer cancel()

    tasks := make(chan Task, 5)
    results := make(chan Result, 5)

    // Start workers with context
    var wg sync.WaitGroup
    for i := 0; i < 3; i++ {
        wg.Add(1)
        go cancellableWorker(ctx, i, tasks, results, &wg)
    }

    // Send tasks
    go func() {
        defer close(tasks)

        for i := 0; i < 10; i++ {
            task := Task{
                ID:   fmt.Sprintf("cancel-task-%d", i),
                Type: "long_running",
                Data: i,
            }

            select {
            case tasks <- task:
                fmt.Printf("Sent task: %s\n", task.ID)
            case <-ctx.Done():
                fmt.Println("Context cancelled, stopping task generation")
                return
            }

            time.Sleep(300 * time.Millisecond)
        }
    }()

    // Close results when all workers are done
    go func() {
        wg.Wait()
        close(results)
    }()

    // Collect results
    var completed, cancelled int
    for result := range results {
        if result.Success {
            fmt.Printf("Completed: %s\n", result.TaskID)
            completed++
        } else {
            fmt.Printf("Cancelled: %s - %v\n", result.TaskID, result.Error)
            cancelled++
        }
    }

    fmt.Printf("Summary: Completed=%d, Cancelled=%d\n", completed, cancelled)
}

func cancellableWorker(ctx context.Context, id int, tasks <-chan Task, results chan<- Result, wg *sync.WaitGroup) {
    defer wg.Done()

    for {
        select {
        case task, ok := <-tasks:
            if !ok {
                return // Channel closed
            }

            result := processCancellableTask(ctx, task, id)

            select {
            case results <- result:
            case <-ctx.Done():
                return
            }

        case <-ctx.Done():
            fmt.Printf("Worker %d: Context cancelled\n", id)
            return
        }
    }
}

func processCancellableTask(ctx context.Context, task Task, workerID int) Result {
    start := time.Now()

    // Create task-specific context with timeout if specified
    taskCtx := ctx
    if task.Timeout > 0 {
        var cancel context.CancelFunc
        taskCtx, cancel = context.WithTimeout(ctx, task.Timeout)
        defer cancel()
    }

    // Simulate work with cancellation checks
    for i := 0; i < 10; i++ {
        select {
        case <-taskCtx.Done():
            return Result{
                TaskID:   task.ID,
                Success:  false,
                Error:    taskCtx.Err(),
                Duration: time.Since(start),
            }
        default:
            time.Sleep(200 * time.Millisecond) // Simulate work
        }
    }

    return Result{
        TaskID:   task.ID,
        Success:  true,
        Output:   fmt.Sprintf("processed by worker %d", workerID),
        Duration: time.Since(start),
    }
}

// AutomationService with context support
type AutomationService struct {
    name     string
    workers  int
    timeout  time.Duration
}

func NewAutomationService(name string, workers int, timeout time.Duration) *AutomationService {
    return &AutomationService{
        name:    name,
        workers: workers,
        timeout: timeout,
    }
}

func (as *AutomationService) ProcessBatch(ctx context.Context, tasks []Task) ([]Result, error) {
    fmt.Printf("\n=== %s: Processing batch of %d tasks ===\n", as.name, len(tasks))

    // Create channels
    taskChan := make(chan Task, len(tasks))
    resultChan := make(chan Result, len(tasks))

    // Start workers
    var wg sync.WaitGroup
    for i := 0; i < as.workers; i++ {
        wg.Add(1)
        go as.worker(ctx, i, taskChan, resultChan, &wg)
    }

    // Send tasks
    go func() {
        defer close(taskChan)
        for _, task := range tasks {
            select {
            case taskChan <- task:
            case <-ctx.Done():
                return
            }
        }
    }()

    // Close results when workers are done
    go func() {
        wg.Wait()
        close(resultChan)
    }()

    // Collect results
    var results []Result
    for result := range resultChan {
        results = append(results, result)
    }

    return results, ctx.Err()
}

func (as *AutomationService) worker(ctx context.Context, id int, tasks <-chan Task, results chan<- Result, wg *sync.WaitGroup) {
    defer wg.Done()

    for {
        select {
        case task, ok := <-tasks:
            if !ok {
                return
            }

            // Process with service timeout
            taskCtx, cancel := context.WithTimeout(ctx, as.timeout)
            result := as.processTask(taskCtx, task, id)
            cancel()

            select {
            case results <- result:
            case <-ctx.Done():
                return
            }

        case <-ctx.Done():
            return
        }
    }
}

func (as *AutomationService) processTask(ctx context.Context, task Task, workerID int) Result {
    start := time.Now()

    // Simulate variable processing time
    processingTime := time.Duration(rand.Intn(1000)+500) * time.Millisecond

    select {
    case <-time.After(processingTime):
        return Result{
            TaskID:   task.ID,
            Success:  true,
            Output:   fmt.Sprintf("processed by %s worker %d", as.name, workerID),
            Duration: time.Since(start),
        }
    case <-ctx.Done():
        return Result{
            TaskID:   task.ID,
            Success:  false,
            Error:    ctx.Err(),
            Duration: time.Since(start),
        }
    }
}

// Failure detection and recovery
func demonstrateFailureDetection() {
    fmt.Println("\n=== Failure Detection and Recovery ===")

    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    // Create service
    service := NewAutomationService("FailureDetectionService", 2, 800*time.Millisecond)

    // Create tasks with varying processing requirements
    tasks := []Task{
        {ID: "fast-1", Type: "fast", Data: "quick task"},
        {ID: "slow-1", Type: "slow", Data: "slow task"},
        {ID: "fast-2", Type: "fast", Data: "another quick task"},
        {ID: "slow-2", Type: "slow", Data: "another slow task"},
        {ID: "fast-3", Type: "fast", Data: "final quick task"},
    }

    results, err := service.ProcessBatch(ctx, tasks)

    if err != nil {
        fmt.Printf("Batch processing error: %v\n", err)
    }

    // Analyze results
    var successful, failed, timedOut int
    for _, result := range results {
        if result.Success {
            successful++
            fmt.Printf("✓ %s: %v (%v)\n", result.TaskID, result.Output, result.Duration)
        } else {
            failed++
            if result.Error == context.DeadlineExceeded {
                timedOut++
                fmt.Printf("⏰ %s: timeout after %v\n", result.TaskID, result.Duration)
            } else {
                fmt.Printf("✗ %s: %v (%v)\n", result.TaskID, result.Error, result.Duration)
            }
        }
    }

    fmt.Printf("\nResults: Successful=%d, Failed=%d (Timeouts=%d)\n",
        successful, failed, timedOut)
}

func main() {
    // Demonstrate drop pattern
    demonstrateDropPattern()

    // Demonstrate cancellation pattern
    demonstrateCancellationPattern()

    // Demonstrate failure detection
    demonstrateFailureDetection()

    fmt.Println("\nAdvanced channel patterns demonstration completed")
}
```

**Prerequisites**: Session 30

---

### Session 32: Testing, Benchmarking, and Performance Analysis

**Duration**: 1 hour (45 min theory + 15 min hands-on)

**Learning Objectives**:

- Master Go testing framework and best practices
- Learn benchmarking techniques and performance analysis
- Understand profiling tools: CPU, memory, execution tracing
- Apply testing strategies to automation systems
- Design comprehensive test suites for automation components

**Videos Covered**:

- 12.1 Basic Unit Testing (0:18:30)
- 12.2 Table Tests (0:16:45)
- 12.3 Sub Tests (0:14:20)
- 13.1 Basic Benchmarks (0:15:30)
- 13.2 Sub Benchmarks (0:12:15)
- 14.1 Profiling Guidelines (0:22:30)

**Key Concepts**:

- Unit testing: structure, assertions, test coverage
- Table-driven tests: testing multiple scenarios
- Benchmarking: measuring performance and memory usage
- Profiling: CPU, memory, and execution analysis
- Test organization and best practices
- Performance optimization techniques

**Hands-on Exercise**:

```go
// Testing, benchmarking, and profiling for automation systems
package main

import (
    "fmt"
    "strings"
    "time"
)

// AutomationTask represents a task in the automation system
type AutomationTask struct {
    ID          string
    Type        string
    Data        []byte
    Priority    int
    CreatedAt   time.Time
    ProcessedAt *time.Time
    Status      TaskStatus
}

type TaskStatus int

const (
    StatusPending TaskStatus = iota
    StatusProcessing
    StatusCompleted
    StatusFailed
)

func (ts TaskStatus) String() string {
    switch ts {
    case StatusPending:
        return "pending"
    case StatusProcessing:
        return "processing"
    case StatusCompleted:
        return "completed"
    case StatusFailed:
        return "failed"
    default:
        return "unknown"
    }
}

// TaskProcessor handles task processing logic
type TaskProcessor struct {
    name         string
    maxDataSize  int
    processingDelay time.Duration
}

func NewTaskProcessor(name string, maxDataSize int, delay time.Duration) *TaskProcessor {
    return &TaskProcessor{
        name:            name,
        maxDataSize:     maxDataSize,
        processingDelay: delay,
    }
}

func (tp *TaskProcessor) Process(task *AutomationTask) error {
    if task == nil {
        return fmt.Errorf("task cannot be nil")
    }

    if task.Status != StatusPending {
        return fmt.Errorf("task %s is not in pending status", task.ID)
    }

    if len(task.Data) > tp.maxDataSize {
        return fmt.Errorf("task data size %d exceeds maximum %d", len(task.Data), tp.maxDataSize)
    }

    // Mark as processing
    task.Status = StatusProcessing

    // Simulate processing time
    time.Sleep(tp.processingDelay)

    // Process based on task type
    switch task.Type {
    case "text":
        return tp.processText(task)
    case "data":
        return tp.processData(task)
    case "file":
        return tp.processFile(task)
    default:
        task.Status = StatusFailed
        return fmt.Errorf("unsupported task type: %s", task.Type)
    }
}

func (tp *TaskProcessor) processText(task *AutomationTask) error {
    // Convert to uppercase
    task.Data = []byte(strings.ToUpper(string(task.Data)))
    task.Status = StatusCompleted
    now := time.Now()
    task.ProcessedAt = &now
    return nil
}

func (tp *TaskProcessor) processData(task *AutomationTask) error {
    // Reverse the data
    data := task.Data
    for i, j := 0, len(data)-1; i < j; i, j = i+1, j-1 {
        data[i], data[j] = data[j], data[i]
    }
    task.Data = data
    task.Status = StatusCompleted
    now := time.Now()
    task.ProcessedAt = &now
    return nil
}

func (tp *TaskProcessor) processFile(task *AutomationTask) error {
    // Add file header
    header := []byte("PROCESSED_FILE:")
    task.Data = append(header, task.Data...)
    task.Status = StatusCompleted
    now := time.Now()
    task.ProcessedAt = &now
    return nil
}

// TaskQueue manages a queue of tasks
type TaskQueue struct {
    tasks    []*AutomationTask
    capacity int
}

func NewTaskQueue(capacity int) *TaskQueue {
    return &TaskQueue{
        tasks:    make([]*AutomationTask, 0, capacity),
        capacity: capacity,
    }
}

func (tq *TaskQueue) Add(task *AutomationTask) error {
    if len(tq.tasks) >= tq.capacity {
        return fmt.Errorf("queue is full (capacity: %d)", tq.capacity)
    }

    tq.tasks = append(tq.tasks, task)
    return nil
}

func (tq *TaskQueue) Next() *AutomationTask {
    if len(tq.tasks) == 0 {
        return nil
    }

    // Find highest priority pending task
    var bestIndex = -1
    var bestPriority = -1

    for i, task := range tq.tasks {
        if task.Status == StatusPending && task.Priority > bestPriority {
            bestIndex = i
            bestPriority = task.Priority
        }
    }

    if bestIndex == -1 {
        return nil
    }

    task := tq.tasks[bestIndex]
    // Remove from queue
    tq.tasks = append(tq.tasks[:bestIndex], tq.tasks[bestIndex+1:]...)
    return task
}

func (tq *TaskQueue) Size() int {
    return len(tq.tasks)
}

func (tq *TaskQueue) PendingCount() int {
    count := 0
    for _, task := range tq.tasks {
        if task.Status == StatusPending {
            count++
        }
    }
    return count
}

// Performance-critical function for benchmarking
func ProcessLargeDataset(data [][]byte, processor *TaskProcessor) ([]*AutomationTask, error) {
    tasks := make([]*AutomationTask, len(data))

    for i, item := range data {
        task := &AutomationTask{
            ID:        fmt.Sprintf("task-%d", i),
            Type:      "data",
            Data:      item,
            Priority:  i % 10,
            CreatedAt: time.Now(),
            Status:    StatusPending,
        }

        if err := processor.Process(task); err != nil {
            return nil, fmt.Errorf("failed to process task %d: %w", i, err)
        }

        tasks[i] = task
    }

    return tasks, nil
}

// Memory-intensive function for memory profiling
func CreateLargeTaskBatch(count int, dataSize int) []*AutomationTask {
    tasks := make([]*AutomationTask, count)

    for i := 0; i < count; i++ {
        data := make([]byte, dataSize)
        for j := range data {
            data[j] = byte(j % 256)
        }

        tasks[i] = &AutomationTask{
            ID:        fmt.Sprintf("large-task-%d", i),
            Type:      "data",
            Data:      data,
            Priority:  i % 10,
            CreatedAt: time.Now(),
            Status:    StatusPending,
        }
    }

    return tasks
}

func main() {
    fmt.Println("=== Automation System Demo ===")

    // Create processor
    processor := NewTaskProcessor("MainProcessor", 1024, 10*time.Millisecond)

    // Create and process some tasks
    tasks := []*AutomationTask{
        {
            ID:        "task-1",
            Type:      "text",
            Data:      []byte("hello world"),
            Priority:  5,
            CreatedAt: time.Now(),
            Status:    StatusPending,
        },
        {
            ID:        "task-2",
            Type:      "data",
            Data:      []byte("automation"),
            Priority:  8,
            CreatedAt: time.Now(),
            Status:    StatusPending,
        },
        {
            ID:        "task-3",
            Type:      "file",
            Data:      []byte("file content"),
            Priority:  3,
            CreatedAt: time.Now(),
            Status:    StatusPending,
        },
    }

    // Process tasks
    for _, task := range tasks {
        if err := processor.Process(task); err != nil {
            fmt.Printf("Error processing %s: %v\n", task.ID, err)
        } else {
            fmt.Printf("Processed %s: %s -> %s\n", task.ID, task.Type, string(task.Data))
        }
    }

    // Demonstrate task queue
    queue := NewTaskQueue(10)
    for _, task := range tasks {
        task.Status = StatusPending // Reset for queue demo
        queue.Add(task)
    }

    fmt.Printf("\nQueue size: %d, Pending: %d\n", queue.Size(), queue.PendingCount())

    // Process from queue by priority
    for queue.PendingCount() > 0 {
        task := queue.Next()
        if task != nil {
            fmt.Printf("Processing from queue: %s (priority %d)\n", task.ID, task.Priority)
        }
    }

    fmt.Println("\nDemo completed. Run tests with: go test -v")
    fmt.Println("Run benchmarks with: go test -bench=.")
    fmt.Println("Run with race detection: go test -race")
    fmt.Println("Profile CPU: go test -cpuprofile=cpu.prof -bench=.")
    fmt.Println("Profile memory: go test -memprofile=mem.prof -bench=.")
}
```

**Test File (main_test.go):**

```go
package main

import (
    "bytes"
    "testing"
    "time"
)

// Basic unit tests
func TestTaskProcessor_Process(t *testing.T) {
    processor := NewTaskProcessor("TestProcessor", 100, 0)

    task := &AutomationTask{
        ID:        "test-1",
        Type:      "text",
        Data:      []byte("hello"),
        Priority:  5,
        CreatedAt: time.Now(),
        Status:    StatusPending,
    }

    err := processor.Process(task)
    if err != nil {
        t.Fatalf("Expected no error, got %v", err)
    }

    if task.Status != StatusCompleted {
        t.Errorf("Expected status %v, got %v", StatusCompleted, task.Status)
    }

    expected := "HELLO"
    if string(task.Data) != expected {
        t.Errorf("Expected data %s, got %s", expected, string(task.Data))
    }
}

// Table-driven tests
func TestTaskProcessor_ProcessTypes(t *testing.T) {
    processor := NewTaskProcessor("TestProcessor", 100, 0)

    tests := []struct {
        name         string
        taskType     string
        inputData    string
        expectedData string
        expectError  bool
    }{
        {
            name:         "text processing",
            taskType:     "text",
            inputData:    "hello world",
            expectedData: "HELLO WORLD",
            expectError:  false,
        },
        {
            name:         "data processing",
            taskType:     "data",
            inputData:    "abc",
            expectedData: "cba",
            expectError:  false,
        },
        {
            name:         "file processing",
            taskType:     "file",
            inputData:    "content",
            expectedData: "PROCESSED_FILE:content",
            expectError:  false,
        },
        {
            name:        "unsupported type",
            taskType:    "unknown",
            inputData:   "data",
            expectError: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            task := &AutomationTask{
                ID:        "test",
                Type:      tt.taskType,
                Data:      []byte(tt.inputData),
                Status:    StatusPending,
                CreatedAt: time.Now(),
            }

            err := processor.Process(task)

            if tt.expectError {
                if err == nil {
                    t.Error("Expected error, got nil")
                }
                return
            }

            if err != nil {
                t.Fatalf("Expected no error, got %v", err)
            }

            if string(task.Data) != tt.expectedData {
                t.Errorf("Expected data %s, got %s", tt.expectedData, string(task.Data))
            }
        })
    }
}

// Sub-tests for task queue
func TestTaskQueue(t *testing.T) {
    t.Run("Add and Size", func(t *testing.T) {
        queue := NewTaskQueue(5)

        task := &AutomationTask{ID: "test", Status: StatusPending}
        err := queue.Add(task)

        if err != nil {
            t.Fatalf("Expected no error, got %v", err)
        }

        if queue.Size() != 1 {
            t.Errorf("Expected size 1, got %d", queue.Size())
        }
    })

    t.Run("Capacity Limit", func(t *testing.T) {
        queue := NewTaskQueue(2)

        // Add tasks up to capacity
        for i := 0; i < 2; i++ {
            task := &AutomationTask{ID: fmt.Sprintf("test-%d", i), Status: StatusPending}
            if err := queue.Add(task); err != nil {
                t.Fatalf("Expected no error for task %d, got %v", i, err)
            }
        }

        // Try to add one more (should fail)
        task := &AutomationTask{ID: "overflow", Status: StatusPending}
        err := queue.Add(task)

        if err == nil {
            t.Error("Expected error when exceeding capacity, got nil")
        }
    })

    t.Run("Priority Ordering", func(t *testing.T) {
        queue := NewTaskQueue(10)

        // Add tasks with different priorities
        priorities := []int{3, 1, 5, 2, 4}
        for i, priority := range priorities {
            task := &AutomationTask{
                ID:       fmt.Sprintf("task-%d", i),
                Priority: priority,
                Status:   StatusPending,
            }
            queue.Add(task)
        }

        // Should get tasks in priority order (highest first)
        expectedOrder := []int{5, 4, 3, 2, 1}
        for _, expectedPriority := range expectedOrder {
            task := queue.Next()
            if task == nil {
                t.Fatal("Expected task, got nil")
            }

            if task.Priority != expectedPriority {
                t.Errorf("Expected priority %d, got %d", expectedPriority, task.Priority)
            }
        }
    })
}

// Benchmarks
func BenchmarkTaskProcessor_ProcessText(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 1000, 0)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        task := &AutomationTask{
            ID:     "bench-task",
            Type:   "text",
            Data:   []byte("benchmark data for processing"),
            Status: StatusPending,
        }
        processor.Process(task)
    }
}

func BenchmarkTaskProcessor_ProcessData(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 1000, 0)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        task := &AutomationTask{
            ID:     "bench-task",
            Type:   "data",
            Data:   bytes.Repeat([]byte("a"), 100),
            Status: StatusPending,
        }
        processor.Process(task)
    }
}

// Sub-benchmarks for different data sizes
func BenchmarkProcessLargeDataset(b *testing.B) {
    processor := NewTaskProcessor("BenchProcessor", 10000, 0)

    sizes := []int{10, 100, 1000}

    for _, size := range sizes {
        b.Run(fmt.Sprintf("size-%d", size), func(b *testing.B) {
            data := make([][]byte, size)
            for i := range data {
                data[i] = bytes.Repeat([]byte("x"), 50)
            }

            b.ResetTimer()
            for i := 0; i < b.N; i++ {
                ProcessLargeDataset(data, processor)
            }
        })
    }
}

// Memory allocation benchmark
func BenchmarkCreateLargeTaskBatch(b *testing.B) {
    b.Run("small-tasks", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            CreateLargeTaskBatch(100, 64)
        }
    })

    b.Run("large-tasks", func(b *testing.B) {
        for i := 0; i < b.N; i++ {
            CreateLargeTaskBatch(10, 1024)
        }
    })
}

// Example test
func ExampleTaskProcessor_Process() {
    processor := NewTaskProcessor("ExampleProcessor", 100, 0)

    task := &AutomationTask{
        ID:        "example-1",
        Type:      "text",
        Data:      []byte("hello"),
        Priority:  5,
        CreatedAt: time.Now(),
        Status:    StatusPending,
    }

    err := processor.Process(task)
    if err != nil {
        fmt.Printf("Error: %v", err)
        return
    }

    fmt.Printf("Processed: %s", string(task.Data))
    // Output: Processed: HELLO
}
```

**Prerequisites**: Session 31

---

## Training Plan Summary and Next Steps

### 🎯 **Training Completion Achievements**

Upon completing this comprehensive 32-session Go training plan, participants will have achieved:

**Core Language Mastery**:

- Deep understanding of Go's type system and memory model
- Expertise in pointers, escape analysis, and memory management
- Proficiency with Go's unique approach to constants and type safety
- Mastery of data structures (arrays, slices, maps, strings)

**Advanced Programming Concepts**:

- Methods and interfaces for clean code architecture
- Composition patterns and decoupling strategies
- Comprehensive error handling techniques
- Package design and code organization

**Concurrency and Performance**:

- Goroutines and the Go scheduler
- Channel patterns and concurrency safety
- Data race detection and prevention
- Performance optimization techniques

**Professional Development Practices**:

- Comprehensive testing strategies (unit, integration, benchmarking)
- Profiling and performance analysis
- Code quality and maintainability practices
- Automation-specific design patterns

### 📈 **Skill Progression Path**

**Beginner → Intermediate (Sessions 1-16)**:

- Foundation concepts and basic programming
- Memory management and type safety
- Data structures and basic algorithms

**Intermediate → Advanced (Sessions 17-24)**:

- Object-oriented concepts in Go
- Interface design and composition
- Error handling mastery

**Advanced → Expert (Sessions 25-32)**:

- Concurrency and parallel programming
- Performance optimization
- Professional testing and profiling

### 🛠 **Practical Application Areas**

Graduates of this training will be equipped to build:

**Automation Systems**:

- Configuration management tools
- Task scheduling and execution engines
- Data processing pipelines
- System monitoring and alerting

**Integration Solutions**:

- API clients and servers
- Message queue processors
- Database integration layers
- File processing systems

**Performance-Critical Applications**:

- High-throughput data processors
- Real-time monitoring systems
- Efficient batch processing tools
- Memory-optimized applications

### 🔄 **Continuous Learning Recommendations**

**Immediate Next Steps**:

1. Build a complete automation project using learned concepts
2. Contribute to open-source Go projects
3. Implement performance-critical components in production
4. Practice advanced concurrency patterns

**Advanced Topics for Further Study**:

- Go modules and dependency management
- Advanced reflection and code generation
- CGO and system programming
- Distributed systems patterns
- Microservices architecture with Go

**Community Engagement**:

- Join Go community forums and discussions
- Attend Go conferences and meetups
- Follow Go team blogs and release notes
- Participate in code reviews and mentoring

### 📚 **Additional Resources**

**Official Documentation**:

- [Go Language Specification](https://golang.org/ref/spec)
- [Effective Go](https://golang.org/doc/effective_go.html)
- [Go Blog](https://blog.golang.org/)

**Advanced Learning**:

- "The Go Programming Language" by Donovan & Kernighan
- "Go in Action" by Kennedy, Ketelsen & St. Martin
- "Concurrency in Go" by Katherine Cox-Buday

**Tools and Utilities**:

- Go toolchain (`go build`, `go test`, `go mod`)
- Profiling tools (`go tool pprof`, `go tool trace`)
- Static analysis tools (`go vet`, `golint`, `staticcheck`)

### 🎉 **Certification and Recognition**

**Internal Certification Path**:

- Complete all 32 sessions with hands-on exercises
- Build and present a capstone automation project
- Demonstrate proficiency in code reviews
- Mentor new team members in Go development

**Professional Development**:

- Contribute to team's Go coding standards
- Lead Go adoption initiatives
- Share knowledge through internal presentations
- Participate in Go community contributions

---

## Conclusion

This comprehensive Go training plan represents a complete journey from beginner to expert-level Go programming, specifically tailored for automation and integration teams. The curriculum is based on the industry-leading "Ultimate Go Programming" course content, enhanced with practical automation examples and modern Go best practices.

The training emphasizes:

- **Practical Application**: Every concept is demonstrated with real-world automation scenarios
- **Performance Awareness**: Deep understanding of memory management and optimization
- **Professional Practices**: Industry-standard testing, profiling, and code quality techniques
- **Progressive Learning**: Each session builds upon previous knowledge systematically

By completing this training, team members will be equipped with the knowledge and skills necessary to build high-performance, maintainable automation systems using Go, contributing effectively to the team's technical objectives and professional growth.

**Training Plan Version**: 2.0
**Last Updated**: July 2025
**Based on**: Ultimate Go Programming 2nd Edition + Go 1.24/1.25 Features
**Target Audience**: Automation and Integration Teams
